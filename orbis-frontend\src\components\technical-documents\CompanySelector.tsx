'use client'

import React, { useState, useEffect } from 'react'
import { CompanySimple, EntrepriseTiers } from '@/types/technical-document'
import { FastAuthService } from '@/lib/auth'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
import { 
  MagnifyingGlassIcon, 
  XMarkIcon, 
  CheckIcon,
  BuildingOfficeIcon 
} from '@heroicons/react/24/outline'

interface CompanySelectorProps {
  selectedCompanies: CompanySimple[]
  onSelectionChange: (companies: CompanySimple[]) => void
  loading?: boolean
  error?: string | null
  disabled?: boolean
}

export default function CompanySelector({
  selectedCompanies,
  onSelectionChange,
  loading = false,
  error = null,
  disabled = false
}: CompanySelectorProps) {
  const [availableCompanies, setAvailableCompanies] = useState<CompanySimple[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [loadingCompanies, setLoadingCompanies] = useState(false)

  // Charger les entreprises disponibles
  const loadCompanies = async () => {
    try {
      setLoadingCompanies(true)
      const token = FastAuthService.getToken()
      const response = await fetch(`${API_BASE_URL}/api/v1/entreprises-tiers`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Erreur lors du chargement des entreprises')
      }

      const entreprisesTiers: EntrepriseTiers[] = await response.json()
      // Convertir les entreprises tiers en format CompanySimple
      const companies: CompanySimple[] = entreprisesTiers.map(entreprise => ({
        id: entreprise.id,
        name: entreprise.nom_entreprise,
        code: entreprise.siret || `ET-${entreprise.id}` // Utiliser SIRET ou un code généré
      }))
      setAvailableCompanies(companies)
    } catch (err) {
      console.error('Erreur lors du chargement des entreprises:', err)
    } finally {
      setLoadingCompanies(false)
    }
  }

  useEffect(() => {
    loadCompanies()
  }, [])

  // Filtrer les entreprises selon la recherche
  const filteredCompanies = availableCompanies.filter(company =>
    company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    company.code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Vérifier si une entreprise est sélectionnée
  const isSelected = (company: CompanySimple) => {
    return selectedCompanies.some(selected => selected.id === company.id)
  }

  // Ajouter/retirer une entreprise
  const toggleCompany = (company: CompanySimple) => {
    if (disabled) return

    const isCurrentlySelected = isSelected(company)
    
    if (isCurrentlySelected) {
      // Retirer l'entreprise
      const newSelection = selectedCompanies.filter(selected => selected.id !== company.id)
      onSelectionChange(newSelection)
    } else {
      // Ajouter l'entreprise
      const newSelection = [...selectedCompanies, company]
      onSelectionChange(newSelection)
    }
  }

  // Retirer une entreprise sélectionnée
  const removeCompany = (companyId: number) => {
    if (disabled) return
    
    const newSelection = selectedCompanies.filter(company => company.id !== companyId)
    onSelectionChange(newSelection)
  }

  return (
    <div className="relative space-y-4">
      {/* Entreprises sélectionnées */}
      {selectedCompanies.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Entreprises associées ({selectedCompanies.length})
          </label>
          <div className="flex flex-wrap gap-2">
            {selectedCompanies.map((company) => (
              <span
                key={company.id}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                <BuildingOfficeIcon className="h-4 w-4 mr-1" />
                {company.name}
                {!disabled && (
                  <button
                    type="button"
                    onClick={() => removeCompany(company.id)}
                    className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-600 hover:bg-blue-200 hover:text-blue-800 focus:outline-none"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                )}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Sélecteur d'entreprises */}
      {!disabled && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ajouter des entreprises
          </label>
          
          <div className="relative">
            {/* Bouton d'ouverture */}
            <button
              type="button"
              onClick={() => setIsOpen(!isOpen)}
              className="relative w-full bg-white border border-gray-300 rounded-md pl-3 pr-10 py-2 text-left cursor-pointer focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              disabled={loading || loadingCompanies}
            >
              <span className="block truncate text-gray-500">
                {loadingCompanies ? 'Chargement...' : 'Sélectionner des entreprises'}
              </span>
              <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </span>
            </button>

            {/* Dropdown */}
            {isOpen && !loadingCompanies && (
              <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                {/* Barre de recherche */}
                <div className="sticky top-0 bg-white p-2 border-b border-gray-200">
                  <div className="relative">
                    <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Rechercher une entreprise..."
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>

                {/* Liste des entreprises */}
                <div className="max-h-48 overflow-y-auto">
                  {filteredCompanies.length === 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      {searchTerm ? 'Aucune entreprise trouvée' : 'Aucune entreprise disponible'}
                    </div>
                  ) : (
                    filteredCompanies.map((company) => (
                      <div
                        key={company.id}
                        onClick={() => toggleCompany(company)}
                        className={`cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50 ${
                          isSelected(company) ? 'bg-blue-50' : ''
                        }`}
                      >
                        <div className="flex items-center">
                          <BuildingOfficeIcon className="h-5 w-5 text-gray-400 mr-3" />
                          <div>
                            <div className="font-medium text-gray-900">{company.name}</div>
                            <div className="text-sm text-gray-500">{company.code}</div>
                          </div>
                        </div>
                        
                        {isSelected(company) && (
                          <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-600">
                            <CheckIcon className="h-5 w-5" />
                          </span>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Bouton pour fermer le dropdown en cliquant à l'extérieur */}
          {isOpen && (
            <div
              className="fixed inset-0 z-0"
              onClick={() => setIsOpen(false)}
            />
          )}
        </div>
      )}

      {/* Message d'erreur */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}

      {/* Indicateur de chargement discret */}
      {loading && (
        <div className="absolute top-2 right-2 flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span className="ml-1 text-xs text-gray-500">Sauvegarde...</span>
        </div>
      )}
    </div>
  )
}
