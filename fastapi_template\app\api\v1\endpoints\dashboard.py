"""
Endpoints pour le dashboard et les statistiques
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func
from app.core.database import get_db
from app.models.project import Project, ProjectStatus
from app.models.user import User, UserRole
from app.api.deps import get_current_user
from pydantic import BaseModel
from datetime import datetime, timedelta
from typing import List, Dict, Any


# Schémas Pydantic
class DashboardStats(BaseModel):
    totalProjects: int
    activeProjects: int
    completedProjects: int
    totalEmployees: int
    totalHours: float
    pendingTasks: int


class ProjectChartData(BaseModel):
    month: str
    projects: int
    completed: int


class DashboardResponse(BaseModel):
    stats: DashboardStats
    chartData: List[ProjectChartData]
    recentActivity: List[Dict[str, Any]]


router = APIRouter()


@router.get("/stats", response_model=DashboardStats)
def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Récupérer les statistiques du dashboard"""
    
    # Statistiques des projets
    total_projects = db.query(Project).filter(
        Project.company_id == current_user.workspace_id,
        Project.is_archived == False
    ).count()
    
    active_projects = db.query(Project).filter(
        Project.company_id == current_user.workspace_id,
        Project.status.in_([ProjectStatus.DAO, ProjectStatus.EXE]),
        Project.is_archived == False
    ).count()
    
    completed_projects = db.query(Project).filter(
        Project.company_id == current_user.workspace_id,
        Project.status == ProjectStatus.COMPLETED,
        Project.is_archived == False
    ).count()
    
    # Statistiques des employés
    total_employees = db.query(User).filter(
        User.company_id == current_user.workspace_id,
        User.is_active == True
    ).count()
    
    # Heures travaillées (simulé pour l'instant)
    total_hours = _calculate_total_hours(db, current_user.workspace_id)
    
    # Tâches en attente (simulé)
    pending_tasks = _calculate_pending_tasks(db, current_user.workspace_id)
    
    return DashboardStats(
        totalProjects=total_projects,
        activeProjects=active_projects,
        completedProjects=completed_projects,
        totalEmployees=total_employees,
        totalHours=total_hours,
        pendingTasks=pending_tasks
    )


@router.get("/chart-data", response_model=List[ProjectChartData])
def get_chart_data(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Récupérer les données pour le graphique d'évolution des projets"""
    
    # Générer les données des 6 derniers mois
    chart_data = []
    current_date = datetime.now()
    
    for i in range(6):
        month_date = current_date - timedelta(days=30 * i)
        month_name = month_date.strftime("%B %Y")
        
        # Projets créés ce mois-là (simulé)
        projects_count = max(0, 5 - i + (i % 2))
        completed_count = max(0, projects_count - 1)
        
        chart_data.append(ProjectChartData(
            month=month_name,
            projects=projects_count,
            completed=completed_count
        ))
    
    return list(reversed(chart_data))


@router.get("/recent-activity")
def get_recent_activity(
    limit: int = 10,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Récupérer l'activité récente"""
    
    # Récupérer les projets récents
    recent_projects = db.query(Project).filter(
        Project.company_id == current_user.workspace_id,
        Project.is_archived == False
    ).order_by(Project.created_at.desc()).limit(limit).all()
    
    activities = []
    for project in recent_projects:
        activities.append({
            "id": project.id,
            "type": "project",
            "title": f"Projet {project.name}",
            "description": f"Client: {project.client_name}",
            "status": project.status.value,
            "date": project.created_at.isoformat(),
            "user": "Système"
        })
    
    return activities


@router.get("/overview")
def get_dashboard_overview(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Récupérer une vue d'ensemble complète du dashboard"""
    
    stats = get_dashboard_stats(db, current_user)
    chart_data = get_chart_data(db, current_user)
    recent_activity = get_recent_activity(5, db, current_user)
    
    return {
        "stats": stats,
        "chartData": chart_data,
        "recentActivity": recent_activity,
        "lastUpdated": datetime.now().isoformat()
    }


def _calculate_total_hours(db: Session, company_id: int) -> float:
    """Calculer le total des heures travaillées (simulé)"""
    # Pour l'instant, on simule avec le nombre de projets * 40h
    project_count = db.query(Project).filter(
        Project.company_id == company_id,
        Project.is_archived == False
    ).count()
    
    return float(project_count * 40.5)


def _calculate_pending_tasks(db: Session, company_id: int) -> int:
    """Calculer le nombre de tâches en attente (simulé)"""
    # Pour l'instant, on simule avec le nombre de projets actifs * 3
    active_projects = db.query(Project).filter(
        Project.company_id == company_id,
        Project.status.in_([ProjectStatus.DAO, ProjectStatus.EXE]),
        Project.is_archived == False
    ).count()
    
    return active_projects * 3
