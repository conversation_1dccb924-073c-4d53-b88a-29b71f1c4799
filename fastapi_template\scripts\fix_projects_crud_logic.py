#!/usr/bin/env python3
"""
Script pour corriger la logique dans projects_crud.py
Remplace la logique complexe ProjectCompany par un simple filtre workspace_id
"""

import re

def fix_projects_crud():
    """Corrige la logique dans projects_crud.py"""
    
    file_path = "app/api/api_v1/endpoints/projects_crud.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"🔄 Correction de {file_path}...")
        
        # Pattern pour trouver les blocs de code à remplacer
        # Cherche les patterns comme:
        # user_workspace = ...
        # if not user_workspace:
        #     raise HTTPException(...)
        # # Filtrer par entreprise via ProjectCompany
        # query = query.join(ProjectCompany).where(...)
        
        # Remplacer tous les blocs de filtrage complexes par un simple filtre workspace_id
        pattern = r'(\s+)# Filtrer par entreprise via ProjectCompany\s+query = query\.join\(ProjectCompany\)\.where\(\s+and_\(\s+ProjectCompany\.company_id\.in_\(workspace_companies_subquery\),\s+ProjectCompany\.is_active == True\s+\)\s+\)'
        
        replacement = r'\1# Filtrer par workspace_id directement\n\1query = query.where(Project.workspace_id == user_workspace.workspace_id)'
        
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Remplacer aussi les patterns dans base_query
        pattern2 = r'(\s+)# Filtrer par entreprise via ProjectCompany\s+base_query = base_query\.join\(ProjectCompany\)\.where\(\s+and_\(\s+ProjectCompany\.company_id\.in_\(workspace_companies_subquery\),\s+ProjectCompany\.is_active == True\s+\)\s+\)'
        
        replacement2 = r'\1# Filtrer par workspace_id directement\n\1base_query = base_query.where(Project.workspace_id == user_workspace.workspace_id)'
        
        content = re.sub(pattern2, replacement2, content, flags=re.MULTILINE)
        
        # Supprimer les définitions de workspace_companies_subquery qui ne sont plus utilisées
        pattern3 = r'\s+# Récupérer les IDs des entreprises tierces du workspace\s+workspace_companies_subquery = select\(EntrepriseTiers\.id\)\.where\(\s+EntrepriseTiers\.workspace_id == user_workspace\.workspace_id\s+\)\s+'
        
        content = re.sub(pattern3, '\n', content, flags=re.MULTILINE)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ {file_path} corrigé avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur lors de la correction de {file_path}: {e}")

if __name__ == "__main__":
    fix_projects_crud()
