{"name": "orbis-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.50.3", "@tinymce/tinymce-react": "^6.2.1", "framer-motion": "^12.18.1", "lucide-react": "^0.525.0", "mammoth": "^1.9.1", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "tinymce": "^7.9.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.5.5", "tailwindcss": "^3.4.0", "typescript": "^5"}}