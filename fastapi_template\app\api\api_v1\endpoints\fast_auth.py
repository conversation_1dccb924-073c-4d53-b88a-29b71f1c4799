# app/api/api_v1/endpoints/fast_auth.py
"""
Endpoints pour l'authentification hybride Supabase + JWT
"""

from typing import Dict, Any
from fastapi import APIRouter, HTTPException, status, Depends, Header
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, EmailStr

from app.core.database import get_db
from app.core.jwt_auth import J<PERSON>TManager
from app.core.supabase_auth import supabase_auth
from app.models.user import User

router = APIRouter()

class LoginRequest(BaseModel):
    email: EmailStr
    password: str

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: Dict[str, Any]
    expires_in: int = 86400  # 24 heures en secondes

class TokenExchangeRequest(BaseModel):
    supabase_token: str

@router.post("/login", response_model=LoginResponse)
async def fast_login(
    login_data: LoginRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Connexion rapide : Supabase auth + JWT token
    """
    try:
        # 1. Authentifier avec Supabase
        supabase_result = await supabase_auth.login_user(
            login_data.email, 
            login_data.password
        )
        
        if not supabase_result.get("user"):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        supabase_user = supabase_result["user"]
        user_email = supabase_user.get("email")
        
        # 2. Récupérer ou créer l'utilisateur local
        result = await db.execute(
            select(User).where(User.email == user_email)
        )
        local_user = result.scalar_one_or_none()
        
        if not local_user:
            # Créer l'utilisateur local s'il n'existe pas
            user_metadata = supabase_user.get("user_metadata", {})
            local_user = User(
                email=user_email,
                first_name=user_metadata.get("first_name", ""),
                last_name=user_metadata.get("last_name", ""),
                role="USER",  # Rôle par défaut
                is_active=True,
                is_verified=bool(supabase_user.get("email_confirmed_at")),
                supabase_user_id=supabase_user.get("id")
            )
            db.add(local_user)
            await db.commit()
            await db.refresh(local_user)
        
        # 3. Déterminer le rôle et les permissions - GESTION ROBUSTE
        # Convertir le rôle en string de manière sûre
        if hasattr(local_user.role, 'value'):
            user_role = local_user.role.value  # Enum Python
        elif hasattr(local_user.role, 'name'):
            user_role = local_user.role.name   # Enum PostgreSQL
        else:
            user_role = str(local_user.role)   # String direct

        print(f"🔍 FastAuth - user_role: '{user_role}', type: {type(user_role)}")
        print(f"🔍 FastAuth - local_user.is_superuser: {local_user.is_superuser}")

        # Utiliser directement le rôle de la base (maintenant en MAJUSCULES)
        normalized_role = user_role  # Garder la valeur originale de la DB

        # Utiliser directement is_superuser de la base de données
        is_superuser = local_user.is_superuser or user_role == "SUPER_ADMIN"
        print(f"🔍 FastAuth - normalized_role: '{normalized_role}', is_superuser final: {is_superuser}")
        
        # 4. Générer le JWT rapide
        jwt_token = JWTManager.create_user_token(
            user_id=local_user.id,
            email=local_user.email,
            role=normalized_role,  # Utiliser le rôle normalisé
            is_superuser=is_superuser
        )

        print(f"🔑 FastAuth - JWT token créé: {jwt_token[:50]}...")
        print(f"🔑 FastAuth - Supabase token: {supabase_result.get('access_token', 'N/A')[:50]}...")
        
        # 5. Préparer la réponse
        user_data = {
            "id": local_user.id,
            "email": local_user.email,
            "first_name": local_user.first_name,
            "last_name": local_user.last_name,
            "role": user_role,
            "is_superuser": is_superuser,
            "is_active": local_user.is_active,
            "is_verified": local_user.is_verified
        }
        
        # Système hybride : retourner le JWT pour les performances
        return LoginResponse(
            access_token=jwt_token,  # JWT pour les API internes
            user=user_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ FastAuth - Erreur de connexion: {str(e)}")
        print(f"❌ FastAuth - Type d'erreur: {type(e)}")
        import traceback
        print(f"❌ FastAuth - Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}"
        )

@router.post("/exchange-token", response_model=LoginResponse)
async def exchange_supabase_token(
    token_data: TokenExchangeRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Échanger un token Supabase contre un JWT rapide
    Utile pour les utilisateurs déjà connectés via Supabase
    """
    try:
        # 1. Vérifier le token Supabase
        supabase_user = await supabase_auth.get_user_from_token(token_data.supabase_token)
        
        if not supabase_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid Supabase token"
            )
        
        user_email = supabase_user.get("email")
        
        # 2. Récupérer l'utilisateur local
        result = await db.execute(
            select(User).where(User.email == user_email)
        )
        local_user = result.scalar_one_or_none()
        
        if not local_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found in local database"
            )
        
        # 3. Générer le JWT - GESTION ROBUSTE DU RÔLE
        # Convertir le rôle en string de manière sûre
        if hasattr(local_user.role, 'value'):
            user_role = local_user.role.value  # Enum Python
        elif hasattr(local_user.role, 'name'):
            user_role = local_user.role.name   # Enum PostgreSQL
        else:
            user_role = str(local_user.role)   # String direct

        # Utiliser directement le rôle de la base (maintenant en MAJUSCULES)
        normalized_role = user_role  # Garder la valeur originale de la DB
        is_superuser = local_user.is_superuser or user_role == "SUPER_ADMIN"

        jwt_token = JWTManager.create_user_token(
            user_id=local_user.id,
            email=local_user.email,
            role=normalized_role,  # Utiliser le rôle normalisé
            is_superuser=is_superuser
        )
        
        # 4. Préparer la réponse
        user_data = {
            "id": local_user.id,
            "email": local_user.email,
            "first_name": local_user.first_name,
            "last_name": local_user.last_name,
            "role": user_role,
            "is_superuser": is_superuser,
            "is_active": local_user.is_active,
            "is_verified": local_user.is_verified
        }
        
        # Système hybride : générer un JWT à partir du token Supabase
        jwt_token = JWTManager.create_user_token(
            user_id=local_user.id,
            email=local_user.email,
            role=normalized_role,  # Utiliser le rôle normalisé
            is_superuser=is_superuser
        )

        return LoginResponse(
            access_token=jwt_token,  # JWT pour les API internes
            user=user_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Erreur lors de l'échange de token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token exchange failed"
        )

@router.get("/verify")
async def verify_jwt_token(
    authorization: str = Header(None)
):
    """
    Vérifier un JWT token rapidement
    """
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header missing"
        )
    
    try:
        scheme, token = authorization.split()
        if scheme.lower() != "bearer":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication scheme"
            )
        
        user_data = JWTManager.extract_user_from_token(token)
        return {
            "valid": True,
            "user": user_data
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
