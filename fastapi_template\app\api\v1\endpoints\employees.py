"""
Endpoints pour la gestion des employés
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.user import User, UserRole
from app.api.deps import get_current_user
from pydantic import BaseModel, EmailStr
from datetime import datetime


# Schémas Pydantic
class EmployeeBase(BaseModel):
    email: EmailStr
    first_name: str
    last_name: str
    phone: Optional[str] = None
    role: UserRole = UserRole.EMPLOYEE


class EmployeeCreate(EmployeeBase):
    password: str


class EmployeeUpdate(BaseModel):
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None


class EmployeeResponse(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    phone: Optional[str] = None
    role: UserRole
    is_active: bool
    company_id: int
    created_at: datetime
    last_login: Optional[datetime] = None

    class Config:
        from_attributes = True


router = APIRouter()


@router.get("/", response_model=List[EmployeeResponse])
def get_employees(
    skip: int = 0,
    limit: int = 100,
    role: Optional[UserRole] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Récupérer la liste des employés de l'entreprise"""
    query = db.query(User).filter(User.company_id == current_user.company_id)
    
    if role:
        query = query.filter(User.role == role)
    
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    
    employees = query.offset(skip).limit(limit).all()
    return employees


@router.get("/{employee_id}", response_model=EmployeeResponse)
def get_employee(
    employee_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Récupérer un employé spécifique"""
    employee = db.query(User).filter(
        User.id == employee_id,
        User.company_id == current_user.company_id
    ).first()
    
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Employé non trouvé"
        )
    
    return employee


@router.post("/", response_model=EmployeeResponse)
def create_employee(
    employee: EmployeeCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Créer un nouveau compte employé"""
    # Vérifier que l'utilisateur actuel a les droits d'admin
    if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Droits insuffisants pour créer un employé"
        )
    
    # Vérifier que l'email n'existe pas déjà
    existing_user = db.query(User).filter(User.email == employee.email).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Un utilisateur avec cet email existe déjà"
        )
    
    # Créer l'utilisateur
    from app.core.security import get_password_hash
    
    db_employee = User(
        email=employee.email,
        first_name=employee.first_name,
        last_name=employee.last_name,
        phone=employee.phone,
        role=employee.role,
        company_id=current_user.company_id,
        hashed_password=get_password_hash(employee.password),
        is_active=True
    )
    
    db.add(db_employee)
    db.commit()
    db.refresh(db_employee)
    
    return db_employee


@router.put("/{employee_id}", response_model=EmployeeResponse)
def update_employee(
    employee_id: int,
    employee_update: EmployeeUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Mettre à jour un employé"""
    # Vérifier que l'utilisateur actuel a les droits d'admin ou modifie son propre profil
    if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER] and current_user.id != employee_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Droits insuffisants pour modifier cet employé"
        )
    
    employee = db.query(User).filter(
        User.id == employee_id,
        User.company_id == current_user.company_id
    ).first()
    
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Employé non trouvé"
        )
    
    # Mettre à jour les champs
    update_data = employee_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(employee, field, value)
    
    db.commit()
    db.refresh(employee)
    
    return employee


@router.delete("/{employee_id}")
def delete_employee(
    employee_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Désactiver un employé (soft delete)"""
    # Vérifier que l'utilisateur actuel a les droits d'admin
    if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Droits insuffisants pour supprimer un employé"
        )
    
    employee = db.query(User).filter(
        User.id == employee_id,
        User.company_id == current_user.company_id
    ).first()
    
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Employé non trouvé"
        )
    
    # Désactiver au lieu de supprimer
    employee.is_active = False
    db.commit()
    
    return {"message": "Employé désactivé avec succès"}
