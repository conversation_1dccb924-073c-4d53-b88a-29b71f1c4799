import React from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'

interface ProjectCardProps {
  project: {
    id: number
    name: string
    description: string
    status: string
    progress: number
    startDate: string
    endDate: string
    budget: number
    manager: string
    employees: number
    location: string
  }
}

export const ProjectCard: React.FC<ProjectCardProps> = ({ project }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'En cours': return 'bg-primary-100 text-primary-800'
      case 'Terminé': return 'bg-primary-100 text-primary-800'
      case 'En attente': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR')
  }

  return (
    <Card className="p-6 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {project.name}
          </h3>
          <p className="text-sm text-gray-600 mb-3">
            {project.description}
          </p>
        </div>
        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(project.status)}`}>
          {project.status}
        </span>
      </div>

      <div className="space-y-3 mb-4">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Progression:</span>
          <span className="font-medium">{project.progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${project.progress}%` }}
          />
        </div>
      </div>

      <div className="space-y-2 mb-4 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Manager:</span>
          <span className="font-medium">{project.manager}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Localisation:</span>
          <span className="font-medium">{project.location}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Budget:</span>
          <span className="font-medium text-green-600">
            {project.budget.toLocaleString('fr-FR')} €
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Équipe:</span>
          <span className="font-medium">{project.employees} personnes</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Échéance:</span>
          <span className="font-medium">{formatDate(project.endDate)}</span>
        </div>
      </div>

      <div className="flex gap-2">
        <Link href={`/projects/${project.id}`} className="flex-1">
          <Button variant="outline" className="w-full">
            Voir détails
          </Button>
        </Link>
        <Button variant="outline" size="sm">
          Modifier
        </Button>
      </div>
    </Card>
  )
}