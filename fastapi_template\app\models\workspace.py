# app/models/workspace.py
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.core.database import Base

class WorkspaceRole(str, enum.Enum):
    """
    Énumération complète des rôles d'espace de travail
    Inclut les rôles de gestion ET les rôles métier BTP
    """
    # Rôles de gestion
    ADMIN = "ADMIN"          # Administrateur d'espace de travail
    MANAGER = "MANAGER"      # Gestionnaire
    USER = "USER"           # Utilisateur standard
    VIEWER = "VIEWER"       # Lecture seule

    # Rôles métier BTP
    MOA = "MOA"             # Maître d'Ouvrage
    MOADEL = "MOADEL"       # Maître d'Ouvrage Délégué
    ARCHI = "ARCHI"         # Architecte
    BE = "BE"               # Bureau d'Études
    BC = "BC"               # Bureau de Contrôle
    OPC = "OPC"             # Ordonnancement Pilotage Coordination
    ENT = "ENT"             # Entreprise
    FO = "FO"               # Fournisseur

class Workspace(Base):
    __tablename__ = "workspaces"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    code = Column(String, unique=True, nullable=False, index=True)
    description = Column(Text)
    address = Column(Text)
    phone = Column(String)
    email = Column(String)
    website = Column(String)
    siret = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    users = relationship("UserWorkspace", back_populates="workspace")
    settings = relationship("WorkspaceSettings", back_populates="workspace", uselist=False)
    suppliers = relationship("Supplier", back_populates="workspace")
    materials = relationship("Material", back_populates="workspace")
    budgets = relationship("Budget", back_populates="workspace")
    purchase_orders = relationship("PurchaseOrder", back_populates="workspace")
    quotes = relationship("Quote", back_populates="workspace")
    documents = relationship("Document", back_populates="workspace")

    # Nouvelles relations RBAC
    role_permissions = relationship("WorkspaceRolePermission", back_populates="workspace", cascade="all, delete-orphan")

    # Relation avec les entreprises tierces
    entreprises_tiers = relationship("EntrepriseTiers", back_populates="workspace", cascade="all, delete-orphan")

    # Relation directe avec les projets
    projects = relationship("Project", back_populates="workspace")

    # IMPORTANT: Les relations avec project_associations et technical_documents sont supprimées
    # car ces entités appartiennent maintenant aux entreprises tierces, pas aux workspaces



class UserWorkspace(Base):
    __tablename__ = "user_workspaces"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    workspace_id = Column(Integer, ForeignKey("workspaces.id"), nullable=False)
    role_name = Column(String(50), nullable=False, default="USER")  # Nom du rôle (plus d'enum)
    is_default = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    invited_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    invited_at = Column(DateTime, nullable=True)
    joined_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="workspaces", foreign_keys=[user_id])
    workspace = relationship("Workspace", back_populates="users")
    inviter = relationship("User", foreign_keys=[invited_by])

    @property
    def role(self):
        """Propriété de compatibilité pour l'ancien système"""
        try:
            return WorkspaceRole(self.role_name)
        except ValueError:
            return WorkspaceRole.USER

    def has_permission(self, permission_name: str) -> bool:
        """
        Vérifie si l'utilisateur a une permission spécifique dans cet espace de travail
        """
        from sqlalchemy.orm import Session
        from app.models.rbac import WorkspaceRolePermission, Permission

        # Pour les tests, on peut utiliser une session depuis l'objet
        if hasattr(self, '_sa_instance_state') and self._sa_instance_state.session:
            session = self._sa_instance_state.session

            # Rechercher la permission
            permission = session.query(Permission).filter(
                Permission.name == permission_name
            ).first()

            if not permission:
                return False

            # Vérifier si le rôle a cette permission dans cet espace de travail
            has_perm = session.query(WorkspaceRolePermission).filter(
                WorkspaceRolePermission.workspace_id == self.workspace_id,
                WorkspaceRolePermission.role_name == self.role_name,
                WorkspaceRolePermission.permission_id == permission.id
            ).first()

            return has_perm is not None

        return False

    def get_permissions(self) -> list:
        """
        Récupère toutes les permissions de l'utilisateur dans cet espace de travail
        """
        from sqlalchemy.orm import Session
        from app.models.rbac import WorkspaceRolePermission, Permission

        if hasattr(self, '_sa_instance_state') and self._sa_instance_state.session:
            session = self._sa_instance_state.session

            permissions = session.query(Permission).join(
                WorkspaceRolePermission,
                WorkspaceRolePermission.permission_id == Permission.id
            ).filter(
                WorkspaceRolePermission.workspace_id == self.workspace_id,
                WorkspaceRolePermission.role_name == self.role_name
            ).all()

            return [perm.name for perm in permissions]

        return []

class WorkspaceSettings(Base):
    __tablename__ = "workspace_settings"

    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id"), nullable=False)
    default_currency = Column(String, default="EUR")
    date_format = Column(String, default="DD/MM/YYYY")
    time_format = Column(String, default="24h")
    language = Column(String, default="fr")
    logo_url = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    workspace = relationship("Workspace", back_populates="settings")


