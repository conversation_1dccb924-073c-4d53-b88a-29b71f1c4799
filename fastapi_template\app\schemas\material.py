# app/schemas/material.py
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from decimal import Decimal

class MaterialBase(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    unit: Optional[str] = None
    current_price: Optional[Decimal] = None
    minimum_quantity: Optional[Decimal] = None
    maximum_quantity: Optional[Decimal] = None
    is_active: Optional[bool] = True

class MaterialCreate(MaterialBase):
    name: str
    code: str
    unit: str
    company_id: int

class MaterialUpdate(MaterialBase):
    pass

class MaterialInDBBase(MaterialBase):
    id: Optional[int] = None
    company_id: Optional[int] = None
    supplier_id: Optional[int] = None
    category_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Material(MaterialInDBBase):
    pass

class MaterialCategory(BaseModel):
    id: Optional[int] = None
    name: str
    code: str
    description: Optional[str] = None
    parent_id: Optional[int] = None

    class Config:
        from_attributes = True

class TechnicalSheet(BaseModel):
    id: Optional[int] = None
    material_id: int
    name: str
    version: Optional[str] = None
    file_path: Optional[str] = None
    specifications: Optional[str] = None

    class Config:
        from_attributes = True

class PriceHistory(BaseModel):
    id: Optional[int] = None
    material_id: int
    price: Decimal
    effective_date: datetime

    class Config:
        from_attributes = True