# app/api/api_v1/endpoints/dev_companies.py
"""
Endpoints temporaires pour le développement - SANS AUTHENTIFICATION
À SUPPRIMER EN PRODUCTION !
"""

from typing import Any, List
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db
from app.models.workspace import Company, UserCompany
from app.models.user import User
from app.api.api_v1.endpoints.admin_companies import AdminCompanyResponse
from app.api.api_v1.endpoints.admin_users import AdminUserResponse

router = APIRouter()

@router.get("/companies", response_model=List[AdminCompanyResponse])
async def dev_list_companies(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100
):
    """
    DÉVELOPPEMENT SEULEMENT - Lister toutes les entreprises sans authentification
    """
    try:
        # Récupérer toutes les entreprises
        result = await db.execute(
            select(Company).offset(skip).limit(limit)
        )
        companies = result.scalars().all()
        
        # Pour chaque entreprise, compter le nombre d'utilisateurs
        companies_with_count = []
        for company in companies:
            user_count_result = await db.execute(
                select(UserCompany).where(UserCompany.company_id == company.id)
            )
            user_count = len(user_count_result.scalars().all())
            
            company_data = AdminCompanyResponse(
                id=company.id,
                name=company.name,
                code=company.code,
                description=company.description,
                address=company.address,
                phone=company.phone,
                email=company.email,
                website=company.website,
                is_active=company.is_active,
                created_at=company.created_at.isoformat() if company.created_at else "",
                updated_at=company.updated_at.isoformat() if company.updated_at else "",
                user_count=user_count
            )
            companies_with_count.append(company_data)
        
        return companies_with_count
    except Exception as e:
        # En cas d'erreur, retourner une liste vide plutôt que de planter
        print(f"Erreur dev_list_companies: {e}")
        return []

@router.get("/companies/{company_id}/users", response_model=List[AdminUserResponse])
async def dev_list_company_users(
    company_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    DÉVELOPPEMENT SEULEMENT - Lister les utilisateurs d'une entreprise sans authentification
    """
    try:
        # Joindre UserCompany avec User pour récupérer les détails
        result = await db.execute(
            select(User, UserCompany)
            .join(UserCompany, User.id == UserCompany.user_id)
            .where(UserCompany.company_id == company_id)
        )
        user_data = result.all()

        if not user_data:
            return []

        users = []
        for user, user_company in user_data:
            # Exclure les super admins
            if user.role and user.role.upper() == "SUPER_ADMIN":
                continue

            users.append(AdminUserResponse(
                id=str(user.id),
                email=user.email or "",
                first_name=user.first_name or "",
                last_name=user.last_name or "",
                role=user.role.value if hasattr(user.role, 'value') else str(user.role) if user.role else "user",
                is_active=user.is_active if user.is_active is not None else True,
                is_verified=user.is_verified if user.is_verified is not None else False,
                created_at=user.created_at.isoformat() if user.created_at else "",
                last_sign_in_at=None,
                company_role=user_company.role.value if hasattr(user_company.role, 'value') else str(user_company.role) if user_company.role else "user",
                user_metadata={}
            ))

        return users
        
    except Exception as e:
        # En cas d'erreur, retourner une liste vide
        print(f"Erreur dev_list_company_users: {e}")
        return []
