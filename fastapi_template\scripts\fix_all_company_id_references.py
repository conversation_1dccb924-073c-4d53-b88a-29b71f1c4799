#!/usr/bin/env python3
"""
Script pour corriger toutes les références company_id -> workspace_id
et autres corrections nécessaires après la migration
"""

import os
import re
import sys
from pathlib import Path

def fix_file_content(file_path: str, content: str) -> str:
    """Applique toutes les corrections nécessaires au contenu d'un fichier"""
    
    # Skip migration files and compatibility files
    if any(skip in file_path for skip in [
        'alembic/versions/', 
        'run_migration.py',
        'deps_old.py',
        'scripts/',  # Skip our own scripts
    ]):
        print(f"   ⏭️  Skipping {file_path} (migration/script file)")
        return content
    
    original_content = content
    
    # 1. Import corrections
    content = re.sub(r'from app\.models\.workspace import.*Company.*', 
                    'from app.models.workspace import Workspace, UserWorkspace, WorkspaceRole', content)
    content = re.sub(r'from app\.models\.rbac import.*CompanyRolePermission.*', 
                    'from app.models.rbac import Role, Permission, WorkspaceRolePermission', content)
    
    # 2. Model class name corrections
    content = re.sub(r'\bUserCompany\b', 'UserWorkspace', content)
    content = re.sub(r'\bCompany\b(?!\w)', 'Workspace', content)
    content = re.sub(r'\bCompanyRole\b', 'WorkspaceRole', content)
    content = re.sub(r'\bCompanyRolePermission\b', 'WorkspaceRolePermission', content)
    
    # 3. Attribute corrections - IMPORTANT: Garder company_id pour les tables qui référencent entreprises_tiers
    # Ne pas changer company_id dans ces contextes :
    # - ProjectCompany.company_id (référence entreprises_tiers)
    # - TechnicalDocumentCompany.company_id (référence entreprises_tiers)
    
    # Mais changer company_id -> workspace_id dans ces contextes :
    # - UserWorkspace.company_id -> UserWorkspace.workspace_id
    # - current_user.company_id -> current_user.workspace_id
    # - user_company.company_id -> user_workspace.workspace_id
    
    # Corrections spécifiques pour les variables et attributs utilisateur
    content = re.sub(r'user_company\.company_id', 'user_workspace.workspace_id', content)
    content = re.sub(r'current_user\.company_id', 'current_user.workspace_id', content)
    content = re.sub(r'UserWorkspace\.company_id', 'UserWorkspace.workspace_id', content)
    
    # Corrections dans les requêtes et filtres
    content = re.sub(r'company_id\s*==\s*current_user\.company_id', 'workspace_id == current_user.workspace_id', content)
    content = re.sub(r'company_id\s*==\s*user_company\.company_id', 'workspace_id == user_workspace.workspace_id', content)
    
    # 4. Variable name corrections
    content = re.sub(r'\buser_company\b', 'user_workspace', content)
    content = re.sub(r'\bcurrent_user_company\b', 'current_user_workspace', content)
    
    # 5. Comment and string corrections
    content = re.sub(r'entreprise de l\'utilisateur', 'espace de travail de l\'utilisateur', content)
    content = re.sub(r'associé à une entreprise', 'associé à un espace de travail', content)
    content = re.sub(r'via UserCompany', 'via UserWorkspace', content)
    content = re.sub(r'company_id spécifique', 'workspace_id spécifique', content)
    
    # 6. Corrections spécifiques pour les paramètres de requête
    content = re.sub(r"'company_id':", "'workspace_id':", content)
    content = re.sub(r'"company_id":', '"workspace_id":', content)
    content = re.sub(r"getattr\(request\.state, 'company_id'", "getattr(request.state, 'workspace_id'", content)
    
    # 7. Corrections dans les logs et prints
    content = re.sub(r'company ID:', 'workspace ID:', content)
    content = re.sub(r'Company ID:', 'Workspace ID:', content)
    
    if content != original_content:
        print(f"   ✅ Fixed {file_path}")
        return content
    else:
        print(f"   ⏭️  No changes needed in {file_path}")
        return content

def fix_all_files():
    """Parcourt tous les fichiers Python et applique les corrections"""
    
    print("🔄 Starting comprehensive company_id -> workspace_id fixes...")
    
    # Directories to process
    directories = [
        'app/api/',
        'app/middleware/',
        'app/schemas/',
        'app/models/',
        'app/services/',
        'app/core/'
    ]
    
    total_files = 0
    fixed_files = 0
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            print(f"   ⚠️  Directory {directory} not found, skipping...")
            continue
            
        print(f"\n📁 Processing {directory}...")
        
        for file_path in dir_path.rglob("*.py"):
            total_files += 1
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    original_content = f.read()
                
                fixed_content = fix_file_content(str(file_path), original_content)
                
                if fixed_content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(fixed_content)
                    fixed_files += 1
                    
            except Exception as e:
                print(f"   ❌ Error processing {file_path}: {e}")
    
    print(f"\n🎉 Comprehensive fixes completed!")
    print(f"📊 Processed {total_files} files, fixed {fixed_files} files")

if __name__ == "__main__":
    # Change to the fastapi_template directory
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    fix_all_files()
