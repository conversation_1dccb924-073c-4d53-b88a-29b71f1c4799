#!/usr/bin/env python3
"""
Script de migration pour créer les tables des documents techniques
"""

import asyncio
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.core.database import engine
from app.models.document import DocumentType

async def create_technical_documents_tables():
    """Créer les tables pour les documents techniques"""

    from app.core.database import engine
    
    # SQL pour créer l'enum DocumentType
    create_enum_sql = """
    DO $$ BEGIN
        CREATE TYPE documenttype AS ENUM ('CCTP', 'DPGF');
    EXCEPTION
        WHEN duplicate_object THEN null;
    END $$;
    """
    
    # SQL pour créer la table technical_documents
    create_technical_documents_sql = """
    CREATE TABLE IF NOT EXISTS technical_documents (
        id SERIAL PRIMARY KEY,
        name VARCHAR NOT NULL,
        type_document documenttype NOT NULL,
        content TEXT,
        project_id INTEGER NOT NULL REFERENCES projects(id),
        created_by INTEGER NOT NULL REFERENCES users(id),
        updated_by INTEGER REFERENCES users(id),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    # SQL pour créer la table de relation technical_document_companies
    create_relation_table_sql = """
    CREATE TABLE IF NOT EXISTS technical_document_companies (
        id SERIAL PRIMARY KEY,
        technical_document_id INTEGER NOT NULL REFERENCES technical_documents(id) ON DELETE CASCADE,
        company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(technical_document_id, company_id)
    );
    """
    
    # SQL pour créer les index
    create_indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_technical_documents_name ON technical_documents(name);",
        "CREATE INDEX IF NOT EXISTS idx_technical_documents_type ON technical_documents(type_document);",
        "CREATE INDEX IF NOT EXISTS idx_technical_documents_project ON technical_documents(project_id);",
        "CREATE INDEX IF NOT EXISTS idx_technical_documents_created_by ON technical_documents(created_by);",
        "CREATE INDEX IF NOT EXISTS idx_technical_documents_active ON technical_documents(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_technical_document_companies_doc ON technical_document_companies(technical_document_id);",
        "CREATE INDEX IF NOT EXISTS idx_technical_document_companies_company ON technical_document_companies(company_id);"
    ]
    
    # SQL pour créer le trigger de mise à jour automatique
    create_trigger_sql = """
    CREATE OR REPLACE FUNCTION update_technical_documents_updated_at()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    $$ language 'plpgsql';
    
    DROP TRIGGER IF EXISTS update_technical_documents_updated_at ON technical_documents;
    CREATE TRIGGER update_technical_documents_updated_at
        BEFORE UPDATE ON technical_documents
        FOR EACH ROW
        EXECUTE FUNCTION update_technical_documents_updated_at();
    """
    
    try:
        async with engine.begin() as conn:
            print("🔄 Création de l'enum DocumentType...")
            await conn.execute(text(create_enum_sql))
            
            print("🔄 Création de la table technical_documents...")
            await conn.execute(text(create_technical_documents_sql))
            
            print("🔄 Création de la table technical_document_companies...")
            await conn.execute(text(create_relation_table_sql))
            
            print("🔄 Création des index...")
            for index_sql in create_indexes_sql:
                await conn.execute(text(index_sql))
            
            print("🔄 Création du trigger de mise à jour...")
            await conn.execute(text(create_trigger_sql))
            
            print("✅ Migration terminée avec succès!")
            
    except Exception as e:
        print(f"❌ Erreur lors de la migration: {e}")
        raise
    finally:
        await engine.dispose()

async def verify_migration():
    """Vérifier que la migration s'est bien déroulée"""

    from app.core.database import engine
    
    verification_queries = [
        "SELECT 1 FROM information_schema.tables WHERE table_name = 'technical_documents';",
        "SELECT 1 FROM information_schema.tables WHERE table_name = 'technical_document_companies';",
        "SELECT 1 FROM information_schema.columns WHERE table_name = 'technical_documents' AND column_name = 'type_document';",
        "SELECT COUNT(*) FROM information_schema.table_constraints WHERE table_name = 'technical_document_companies' AND constraint_type = 'UNIQUE';"
    ]
    
    try:
        async with engine.begin() as conn:
            print("\n🔍 Vérification de la migration...")
            
            # Vérifier l'existence des tables
            result = await conn.execute(text(verification_queries[0]))
            if not result.scalar():
                raise Exception("Table technical_documents non trouvée")
            
            result = await conn.execute(text(verification_queries[1]))
            if not result.scalar():
                raise Exception("Table technical_document_companies non trouvée")
            
            # Vérifier la colonne type_document
            result = await conn.execute(text(verification_queries[2]))
            if not result.scalar():
                raise Exception("Colonne type_document non trouvée")
            
            # Vérifier la contrainte d'unicité
            result = await conn.execute(text(verification_queries[3]))
            if result.scalar() == 0:
                raise Exception("Contrainte d'unicité non trouvée")
            
            print("✅ Vérification réussie - toutes les structures sont en place!")
            
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        raise
    finally:
        await engine.dispose()

async def create_sample_data():
    """Créer des données d'exemple (optionnel)"""

    from app.core.database import engine
    
    # Vérifier s'il y a des projets et utilisateurs existants
    check_data_sql = """
    SELECT 
        (SELECT COUNT(*) FROM projects) as project_count,
        (SELECT COUNT(*) FROM users) as user_count,
        (SELECT COUNT(*) FROM companies) as company_count;
    """
    
    try:
        async with engine.begin() as conn:
            result = await conn.execute(text(check_data_sql))
            row = result.fetchone()
            
            if not row or row.project_count == 0 or row.user_count == 0 or row.company_count == 0:
                print("⚠️  Pas assez de données de base pour créer des exemples")
                print(f"   Projets: {row.project_count if row else 0}")
                print(f"   Utilisateurs: {row.user_count if row else 0}")
                print(f"   Entreprises: {row.company_count if row else 0}")
                return
            
            # Créer un document CCTP d'exemple
            sample_cctp_sql = """
            INSERT INTO technical_documents (name, type_document, content, project_id, created_by)
            SELECT 
                'CCTP - Gros œuvre',
                'CCTP',
                '<h1>CCTP - Gros œuvre</h1>
                <h2>Article 1 - Terrassements</h2>
                <div class="technical-spec">
                    <h3>Spécifications techniques</h3>
                    <p>Les terrassements seront exécutés conformément aux plans et aux spécifications du DTU 12.</p>
                </div>
                <h3>Mise en œuvre</h3>
                <p>Les travaux de terrassement comprennent...</p>',
                (SELECT id FROM projects LIMIT 1),
                (SELECT id FROM users LIMIT 1)
            WHERE NOT EXISTS (SELECT 1 FROM technical_documents WHERE name = 'CCTP - Gros œuvre');
            """
            
            # Créer un document DPGF d'exemple
            sample_dpgf_sql = """
            INSERT INTO technical_documents (name, type_document, content, project_id, created_by)
            SELECT 
                'DPGF - Maçonnerie',
                'DPGF',
                '<h1>DPGF - Maçonnerie</h1>
                <h2>Poste 01 - Fondations</h2>
                <table>
                    <tr><th>Désignation</th><th>Unité</th><th>Quantité</th><th>Prix unitaire</th><th>Prix total</th></tr>
                    <tr><td>Fouilles en rigole</td><td>m³</td><td>25.00</td><td>45.00 €</td><td>1 125.00 €</td></tr>
                    <tr><td>Béton de propreté</td><td>m³</td><td>5.00</td><td>120.00 €</td><td>600.00 €</td></tr>
                </table>',
                (SELECT id FROM projects LIMIT 1),
                (SELECT id FROM users LIMIT 1)
            WHERE NOT EXISTS (SELECT 1 FROM technical_documents WHERE name = 'DPGF - Maçonnerie');
            """
            
            print("🔄 Création de données d'exemple...")
            await conn.execute(text(sample_cctp_sql))
            await conn.execute(text(sample_dpgf_sql))
            
            # Associer les documents aux entreprises
            associate_companies_sql = """
            INSERT INTO technical_document_companies (technical_document_id, company_id)
            SELECT td.id, c.id
            FROM technical_documents td
            CROSS JOIN (SELECT id FROM companies LIMIT 2) c
            WHERE td.name IN ('CCTP - Gros œuvre', 'DPGF - Maçonnerie')
            ON CONFLICT (technical_document_id, company_id) DO NOTHING;
            """
            
            await conn.execute(text(associate_companies_sql))
            
            print("✅ Données d'exemple créées!")
            
    except Exception as e:
        print(f"❌ Erreur lors de la création des données d'exemple: {e}")
        # Ne pas faire échouer la migration pour les données d'exemple
    finally:
        await engine.dispose()

async def main():
    """Fonction principale"""
    print("🚀 Début de la migration des documents techniques")
    print("=" * 50)
    
    try:
        # Étape 1: Créer les tables
        await create_technical_documents_tables()
        
        # Étape 2: Vérifier la migration
        await verify_migration()
        
        # Étape 3: Créer des données d'exemple (optionnel)
        create_samples = input("\n❓ Voulez-vous créer des données d'exemple ? (y/N): ").lower().strip()
        if create_samples in ['y', 'yes', 'oui']:
            await create_sample_data()
        
        print("\n🎉 Migration terminée avec succès!")
        print("\nProchaines étapes:")
        print("1. Redémarrer le serveur FastAPI")
        print("2. Tester les endpoints /api/v1/technical-documents")
        print("3. Configurer la clé API OpenAI dans les variables d'environnement")
        
    except Exception as e:
        print(f"\n💥 Échec de la migration: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
