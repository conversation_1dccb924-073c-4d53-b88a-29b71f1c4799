# app/schemas/project.py
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from app.models.project import ProjectStatus, ProjectNature

class ProjectBase(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    status: Optional[ProjectStatus] = ProjectStatus.EN_COURS
    nature: Optional[ProjectNature] = ProjectNature.DEVIS
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    budget_total: Optional[Decimal] = None
    address: Optional[str] = None
    client_name: Optional[str] = None
    client_contact: Optional[str] = None
    is_archived: Optional[bool] = False

class ProjectCreate(ProjectBase):
    name: str
    code: Optional[str] = None  # Sera généré automatiquement si non fourni

class ProjectUpdate(ProjectBase):
    pass

class ProjectCompany(BaseModel):
    """Schéma pour la relation projet-entreprise"""
    id: Optional[int] = None
    project_id: int
    workspace_id: int
    role: str = "OWNER"
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ProjectInDBBase(ProjectBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ProjectResponse(ProjectInDBBase):
    """Schéma de réponse simplifié pour les API"""
    pass

class Project(ProjectInDBBase):
    """Schéma complet du projet avec ses relations"""
    companies: Optional[List[ProjectCompany]] = []

    @property
    def primary_company_id(self) -> Optional[int]:
        """Retourne l'ID de l'entreprise propriétaire"""
        for pc in self.companies:
            if pc.role == "OWNER" and pc.is_active:
                return pc.workspace_id
        return None

class ProjectDocument(BaseModel):
    id: Optional[int] = None
    project_id: int
    document_id: int
    folder_type: Optional[str] = None
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ProjectEmployee(BaseModel):
    id: Optional[int] = None
    project_id: int
    employee_id: int
    role: Optional[str] = None
    hourly_rate: Optional[Decimal] = None
    assigned_at: Optional[datetime] = None
    is_active: Optional[bool] = True

    class Config:
        from_attributes = True