# app/api/api_v1/endpoints/financial.py
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.models.financial import Budget, BudgetLine, Invoice, Payment, FinancialReport
from app.schemas.financial import Budget as BudgetSchema, BudgetCreate, BudgetUpdate, BudgetLine as BudgetLineSchema, Invoice as InvoiceSchema, InvoiceCreate, Payment as PaymentSchema, FinancialReport as FinancialReportSchema

router = APIRouter()

@router.get("/budgets/", response_model=List[BudgetSchema])
def read_budgets(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    company_id: int = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve budgets.
    """
    if company_id:
        deps.get_company_access(company_id, current_user, db)
        budgets = db.query(Budget).filter(Budget.company_id == company_id).offset(skip).limit(limit).all()
    else:
        from app.models.workspace import UserCompany
        user_companies = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).all()
        company_ids = [uc.company_id for uc in user_companies]
        budgets = db.query(Budget).filter(Budget.company_id.in_(company_ids)).offset(skip).limit(limit).all()
    
    return budgets

@router.post("/budgets/", response_model=BudgetSchema)
def create_budget(
    *,
    db: Session = Depends(deps.get_db),
    budget_in: BudgetCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new budget.
    """
    deps.get_company_access(budget_in.company_id, current_user, db)
    
    budget = Budget(**budget_in.dict())
    db.add(budget)
    db.commit()
    db.refresh(budget)
    return budget

@router.get("/budgets/{id}", response_model=BudgetSchema)
def read_budget(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get budget by ID.
    """
    budget = db.query(Budget).filter(Budget.id == id).first()
    if not budget:
        raise HTTPException(status_code=404, detail="Budget not found")
    
    deps.get_company_access(budget.company_id, current_user, db)
    return budget

@router.put("/budgets/{id}", response_model=BudgetSchema)
def update_budget(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    budget_in: BudgetUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a budget.
    """
    budget = db.query(Budget).filter(Budget.id == id).first()
    if not budget:
        raise HTTPException(status_code=404, detail="Budget not found")
    
    deps.get_company_access(budget.company_id, current_user, db)
    
    for field, value in budget_in.dict(exclude_unset=True).items():
        setattr(budget, field, value)
    
    db.add(budget)
    db.commit()
    db.refresh(budget)
    return budget

@router.post("/budgets/{id}/lines", response_model=BudgetLineSchema)
def create_budget_line(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    line_in: BudgetLineSchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create budget line.
    """
    budget = db.query(Budget).filter(Budget.id == id).first()
    if not budget:
        raise HTTPException(status_code=404, detail="Budget not found")
    
    deps.get_company_access(budget.company_id, current_user, db)
    
    line = BudgetLine(**line_in.dict(exclude={'id'}), budget_id=id)
    db.add(line)
    db.commit()
    db.refresh(line)
    return line

@router.get("/invoices/", response_model=List[InvoiceSchema])
def read_invoices(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    company_id: int = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve invoices.
    """
    if company_id:
        deps.get_company_access(company_id, current_user, db)
        invoices = db.query(Invoice).filter(Invoice.company_id == company_id).offset(skip).limit(limit).all()
    else:
        from app.models.workspace import UserCompany
        user_companies = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).all()
        company_ids = [uc.company_id for uc in user_companies]
        invoices = db.query(Invoice).filter(Invoice.company_id.in_(company_ids)).offset(skip).limit(limit).all()
    
    return invoices

@router.post("/invoices/", response_model=InvoiceSchema)
def create_invoice(
    *,
    db: Session = Depends(deps.get_db),
    invoice_in: InvoiceCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new invoice.
    """
    deps.get_company_access(invoice_in.company_id, current_user, db)
    
    # Check if invoice number already exists for this company
    existing = db.query(Invoice).filter(
        Invoice.company_id == invoice_in.company_id,
        Invoice.invoice_number == invoice_in.invoice_number
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="Invoice number already exists for this company")
    
    invoice = Invoice(**invoice_in.dict())
    db.add(invoice)
    db.commit()
    db.refresh(invoice)
    return invoice

@router.get("/invoices/{id}", response_model=InvoiceSchema)
def read_invoice(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get invoice by ID.
    """
    invoice = db.query(Invoice).filter(Invoice.id == id).first()
    if not invoice:
        raise HTTPException(status_code=404, detail="Invoice not found")
    
    deps.get_company_access(invoice.company_id, current_user, db)
    return invoice

@router.post("/invoices/{id}/payments", response_model=PaymentSchema)
def create_payment(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    payment_in: PaymentSchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create payment for invoice.
    """
    invoice = db.query(Invoice).filter(Invoice.id == id).first()
    if not invoice:
        raise HTTPException(status_code=404, detail="Invoice not found")
    
    deps.get_company_access(invoice.company_id, current_user, db)
    
    payment = Payment(**payment_in.dict(exclude={'id'}), invoice_id=id)
    db.add(payment)
    db.commit()
    db.refresh(payment)
    return payment

@router.get("/reports/", response_model=List[FinancialReportSchema])
def read_financial_reports(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    company_id: int = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve financial reports.
    """
    if company_id:
        deps.get_company_access(company_id, current_user, db)
        reports = db.query(FinancialReport).filter(FinancialReport.company_id == company_id).offset(skip).limit(limit).all()
    else:
        from app.models.workspace import UserCompany
        user_companies = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).all()
        company_ids = [uc.company_id for uc in user_companies]
        reports = db.query(FinancialReport).filter(FinancialReport.company_id.in_(company_ids)).offset(skip).limit(limit).all()
    
    return reports

@router.post("/reports/", response_model=FinancialReportSchema)
def create_financial_report(
    *,
    db: Session = Depends(deps.get_db),
    report_in: FinancialReportSchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new financial report.
    """
    deps.get_company_access(report_in.company_id, current_user, db)
    
    report = FinancialReport(**report_in.dict(exclude={'id'}))
    db.add(report)
    db.commit()
    db.refresh(report)
    return report