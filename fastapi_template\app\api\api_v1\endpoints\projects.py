# app/api/api_v1/endpoints/projects.py
from typing import Any, List, Dict
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy import select

from app.api import deps
from app.models.user import User
from app.models.workspace import UserCompany, Company
from app.models.project import Project, ProjectEmployee
from app.models.employee import Employee
from app.schemas.project import Project as ProjectSchema, ProjectCreate, ProjectUpdate
from app.core.jwt_auth import require_auth

router = APIRouter()

@router.get("/", response_model=List[ProjectSchema])
async def read_projects(
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    company_id: int = None,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Retrieve projects.
    """
    # Si l'utilisateur est SUPER_ADMIN, il a accès à tous les projets
    user_role = current_user.get("role", "").upper()
    is_superuser = current_user.get("is_superuser", False)
    user_id = current_user.get("user_id")

    if user_role == "SUPER_ADMIN" or is_superuser:
        if company_id:
            result = await db.execute(select(Project).filter(Project.company_id == company_id).offset(skip).limit(limit))
            projects = result.scalars().all()
        else:
            result = await db.execute(select(Project).offset(skip).limit(limit))
            projects = result.scalars().all()
    else:
        if company_id:
            # Pour les utilisateurs non-super admin, vérifier l'accès à l'entreprise
            from app.models.workspace import UserCompany
            result = await db.execute(select(UserCompany).filter(
                UserCompany.user_id == user_id,
                UserCompany.company_id == company_id
            ))
            user_company = result.scalar_one_or_none()
            if not user_company:
                raise HTTPException(status_code=403, detail="Access denied to this company")

            result = await db.execute(select(Project).filter(Project.company_id == company_id).offset(skip).limit(limit))
            projects = result.scalars().all()
        else:
            # Get all projects from user's companies
            from app.models.workspace import UserCompany
            result = await db.execute(select(UserCompany).filter(UserCompany.user_id == user_id))
            user_companies = result.scalars().all()
            company_ids = [uc.company_id for uc in user_companies]
            result = await db.execute(select(Project).filter(Project.company_id.in_(company_ids)).offset(skip).limit(limit))
            projects = result.scalars().all()

    return projects

@router.post("/", response_model=ProjectSchema)
async def create_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_in: ProjectCreate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Create new project.
    """
    # Récupérer l'ID utilisateur depuis le token JWT
    user_id = current_user.get("user_id")
    if not user_id:
        raise HTTPException(status_code=400, detail="Invalid user token")

    # Récupérer l'utilisateur complet depuis la base de données
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Déterminer l'entreprise à utiliser
    if current_user.get("is_superuser", False):
        # Super admin : peut spécifier une entreprise ou utiliser la première disponible
        if project_in.company_id:
            company_id = project_in.company_id
        else:
            # Récupérer la première entreprise disponible pour le super admin
            company_result = await db.execute(select(Company).limit(1))
            company = company_result.scalar_one_or_none()
            if not company:
                raise HTTPException(status_code=400, detail="No company found. Please create a company first.")
            company_id = company.id
    else:
        # Utilisateur normal : utiliser son entreprise (récupérer depuis UserCompany)
        user_company_result = await db.execute(
            select(UserCompany).where(
                UserCompany.user_id == user_id,
                UserCompany.is_active == True
            ).limit(1)
        )
        user_company = user_company_result.scalar_one_or_none()
        if not user_company:
            raise HTTPException(status_code=403, detail="User not associated with any company")

        company_id = user_company.company_id

        # Si un company_id est spécifié dans la requête, vérifier que l'utilisateur y a accès
        if project_in.company_id and project_in.company_id != company_id:
            # Vérifier si l'utilisateur a accès à cette autre entreprise
            other_company_result = await db.execute(
                select(UserCompany).where(
                    UserCompany.user_id == user_id,
                    UserCompany.company_id == project_in.company_id,
                    UserCompany.is_active == True
                )
            )
            other_company = other_company_result.scalar_one_or_none()
            if not other_company:
                raise HTTPException(status_code=403, detail="Access denied to the specified company")
            company_id = project_in.company_id

    # L'accès à l'entreprise a déjà été vérifié ci-dessus

    # Check if code already exists for this company
    existing_result = await db.execute(
        select(Project).filter(
            Project.company_id == company_id,
            Project.code == project_in.code
        )
    )
    existing = existing_result.scalar_one_or_none()
    if existing:
        raise HTTPException(status_code=400, detail="Project code already exists for this company")

    # Créer le projet avec l'ID de l'entreprise correct
    project_data = project_in.dict()
    project_data["company_id"] = company_id

    # Convertir les dates avec timezone en dates sans timezone pour PostgreSQL
    if project_data.get("start_date") and hasattr(project_data["start_date"], 'replace'):
        project_data["start_date"] = project_data["start_date"].replace(tzinfo=None)
    if project_data.get("end_date") and hasattr(project_data["end_date"], 'replace'):
        project_data["end_date"] = project_data["end_date"].replace(tzinfo=None)

    project = Project(**project_data)

    db.add(project)
    await db.commit()
    await db.refresh(project)
    return project

@router.get("/{id}", response_model=ProjectSchema)
async def read_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    id: int,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Get project by ID.
    """
    result = await db.execute(select(Project).where(Project.id == id))
    project = result.scalar_one_or_none()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    # Vérifier l'accès à l'entreprise
    user_id = current_user.get("user_id")
    if not current_user.get("is_superuser", False):
        user_company_result = await db.execute(
            select(UserCompany).where(
                UserCompany.user_id == user_id,
                UserCompany.company_id == project.company_id
            )
        )
        user_company = user_company_result.scalar_one_or_none()
        if not user_company:
            raise HTTPException(status_code=403, detail="Access denied to this company")

    return project

@router.put("/{id}", response_model=ProjectSchema)
async def update_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    id: int,
    project_in: ProjectUpdate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Update a project.
    """
    result = await db.execute(select(Project).where(Project.id == id))
    project = result.scalar_one_or_none()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    # Vérifier l'accès à l'entreprise
    user_id = current_user.get("user_id")
    if not current_user.get("is_superuser", False):
        user_company_result = await db.execute(
            select(UserCompany).where(
                UserCompany.user_id == user_id,
                UserCompany.company_id == project.company_id
            )
        )
        user_company = user_company_result.scalar_one_or_none()
        if not user_company:
            raise HTTPException(status_code=403, detail="Access denied to this company")

    for field, value in project_in.dict(exclude_unset=True).items():
        setattr(project, field, value)

    db.add(project)
    await db.commit()
    await db.refresh(project)
    return project

@router.post("/{id}/employees", response_model=dict)
async def assign_employee_to_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    id: int,
    employee_id: int,
    role: str = None,
    hourly_rate: float = None,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Assign employee to project.
    """
    result = await db.execute(select(Project).where(Project.id == id))
    project = result.scalar_one_or_none()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    # Vérifier l'accès à l'entreprise
    user_id = current_user.get("user_id")
    if not current_user.get("is_superuser", False):
        user_company_result = await db.execute(
            select(UserCompany).where(
                UserCompany.user_id == user_id,
                UserCompany.company_id == project.company_id
            )
        )
        user_company = user_company_result.scalar_one_or_none()
        if not user_company:
            raise HTTPException(status_code=403, detail="Access denied to this company")

    employee_result = await db.execute(
        select(Employee).where(
            Employee.id == employee_id,
            Employee.company_id == project.company_id
        )
    )
    employee = employee_result.scalar_one_or_none()
    if not employee:
        raise HTTPException(status_code=404, detail="Employee not found")

    # Check if already assigned
    existing_result = await db.execute(
        select(ProjectEmployee).where(
            ProjectEmployee.project_id == id,
            ProjectEmployee.employee_id == employee_id,
            ProjectEmployee.is_active == True
        )
    )
    existing = existing_result.scalar_one_or_none()
    if existing:
        raise HTTPException(status_code=400, detail="Employee already assigned to project")

    assignment = ProjectEmployee(
        project_id=id,
        employee_id=employee_id,
        role=role,
        hourly_rate=hourly_rate
    )
    db.add(assignment)
    await db.commit()

    return {"message": "Employee assigned successfully"}

@router.delete("/{id}/employees/{employee_id}")
async def remove_employee_from_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    id: int,
    employee_id: int,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Remove employee from project.
    """
    result = await db.execute(select(Project).where(Project.id == id))
    project = result.scalar_one_or_none()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    # Vérifier l'accès à l'entreprise
    user_id = current_user.get("user_id")
    if not current_user.get("is_superuser", False):
        user_company_result = await db.execute(
            select(UserCompany).where(
                UserCompany.user_id == user_id,
                UserCompany.company_id == project.company_id
            )
        )
        user_company = user_company_result.scalar_one_or_none()
        if not user_company:
            raise HTTPException(status_code=403, detail="Access denied to this company")

    assignment_result = await db.execute(
        select(ProjectEmployee).where(
            ProjectEmployee.project_id == id,
            ProjectEmployee.employee_id == employee_id
        )
    )
    assignment = assignment_result.scalar_one_or_none()
    if not assignment:
        raise HTTPException(status_code=404, detail="Assignment not found")

    assignment.is_active = False
    db.add(assignment)
    await db.commit()

    return {"message": "Employee removed from project"}
