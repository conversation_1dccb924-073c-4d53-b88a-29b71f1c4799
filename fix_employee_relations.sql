-- Script pour corriger les relations Employee
-- Les employés doivent être associés aux entreprises_tiers, pas aux workspaces

-- 1. Ajouter la nouvelle colonne entreprise_tiers_id
ALTER TABLE employees ADD COLUMN entreprise_tiers_id INTEGER;

-- 2. C<PERSON>er une entreprise_tiers par défaut si elle n'existe pas
INSERT INTO entreprises_tiers (
    nom_entreprise, 
    activite, 
    adresse, 
    code_postal, 
    ville, 
    telephone, 
    email, 
    siret,
    workspace_id,
    created_at,
    updated_at
) 
SELECT 
    'Entreprise par défaut',
    'Non spécifiée',
    'Adresse non spécifiée',
    '00000',
    'Ville non spécifiée',
    'Non spécifié',
    '<EMAIL>',
    'SIRET_DEFAULT',
    1, -- Premier workspace
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM entreprises_tiers WHERE nom_entreprise = 'Entreprise par défaut'
);

-- 3. Mettre à jour tous les employés pour qu'ils pointent vers cette entreprise_tiers
UPDATE employees 
SET entreprise_tiers_id = (
    SELECT id FROM entreprises_tiers 
    WHERE nom_entreprise = 'Entreprise par défaut' 
    LIMIT 1
)
WHERE entreprise_tiers_id IS NULL;

-- 4. Ajouter la contrainte NOT NULL
ALTER TABLE employees ALTER COLUMN entreprise_tiers_id SET NOT NULL;

-- 5. Ajouter la contrainte de clé étrangère
ALTER TABLE employees 
ADD CONSTRAINT fk_employees_entreprise_tiers 
FOREIGN KEY (entreprise_tiers_id) REFERENCES entreprises_tiers(id);

-- 6. Supprimer l'ancienne colonne workspace_id
ALTER TABLE employees DROP COLUMN workspace_id;

-- 7. Créer un index pour les performances
CREATE INDEX idx_employees_entreprise_tiers_id ON employees(entreprise_tiers_id);
