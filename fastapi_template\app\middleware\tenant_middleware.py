# app/middleware/tenant_middleware.py
from typing import Optional
from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import logging

from app.core.database import AsyncSessionLocal
from app.models.user import User
from app.models.workspace import UserWorkspace, Workspace
from app.core import security

logger = logging.getLogger(__name__)


class TenantContextMiddleware(BaseHTTPMiddleware):
    """
    Middleware pour gérer le contexte multi-tenant
    Ajoute les informations de l'entreprise courante à la requête
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.excluded_paths = {
            "/docs", "/redoc", "/openapi.json", "/health", 
            "/api/v1/auth/login", "/api/v1/auth/register",
            "/api/v1/public"
        }
    
    async def dispatch(self, request: Request, call_next):
        # Skip middleware for excluded paths
        if any(request.url.path.startswith(path) for path in self.excluded_paths):
            return await call_next(request)
        
        # Initialize tenant context
        request.state.workspace_id = None
        request.state.company = None
        request.state.user_company_role = None
        
        # Extract token from Authorization header
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return await call_next(request)
        
        token = auth_header.split(" ")[1]
        
        try:
            # Verify token and get user info
            payload = security.verify_token(token, token_type="access")
            if not payload or not payload.get("sub"):
                return await call_next(request)
            
            user_id = payload.get("sub")
            
            # Get user's company context
            async with AsyncSessionLocal() as db:
                # Get user
                result = await db.execute(select(User).where(User.id == user_id))
                user = result.scalar_one_or_none()
                
                if not user:
                    return await call_next(request)
                
                # Get user's default company or first active company
                result = await db.execute(
                    select(UserWorkspace, Workspace)
                    .join(Workspace, UserWorkspace.workspace_id == Workspace.id)
                    .where(
                        UserWorkspace.user_id == user.id,
                        UserWorkspace.is_active == True,
                        Workspace.is_active == True
                    )
                    .order_by(UserWorkspace.is_default.desc(), UserWorkspace.created_at.asc())
                )
                user_company_data = result.first()
                
                if user_company_data:
                    user_workspace, company = user_company_data
                    request.state.workspace_id = company.id
                    request.state.company = company
                    request.state.user_company_role = user_workspace.role
                    request.state.user = user
                    
                    logger.debug(f"Tenant context set: user={user.email}, company={company.name}, role={user_workspace.role}")
        
        except Exception as e:
            logger.warning(f"Error setting tenant context: {e}")
            # Continue without tenant context
        
        return await call_next(request)


def get_current_company_id(request: Request) -> Optional[int]:
    """Get current company ID from request state"""
    return getattr(request.state, 'company_id', None)


def get_current_company(request: Request) -> Optional[Workspace]:
    """Get current company from request state"""
    return getattr(request.state, 'company', None)


def get_current_user_role(request: Request) -> Optional[str]:
    """Get current user's role in the company from request state"""
    return getattr(request.state, 'user_company_role', None)


def require_company_access(request: Request) -> Workspace:
    """Require valid company access, raise exception if not available"""
    company = get_current_company(request)
    if not company:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="No company access available"
        )
    return company


def require_company_role(request: Request, required_roles: list) -> str:
    """Require specific company role, raise exception if not sufficient"""
    role = get_current_user_role(request)
    if not role or role not in required_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Insufficient permissions. Required roles: {required_roles}"
        )
    return role
