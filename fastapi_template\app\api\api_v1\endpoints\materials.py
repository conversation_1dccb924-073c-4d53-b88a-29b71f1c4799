# app/api/api_v1/endpoints/materials.py
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.models.material import Material, MaterialCategory, TechnicalSheet, PriceHistory
from app.schemas.material import Material as MaterialSchema, MaterialCreate, MaterialUpdate, MaterialCategory as MaterialCategorySchema, TechnicalSheet as TechnicalSheetSchema, PriceHistory as PriceHistorySchema

router = APIRouter()

@router.get("/", response_model=List[MaterialSchema])
def read_materials(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    company_id: int = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve materials.
    """
    if company_id:
        deps.get_workspace_access_sync(company_id, current_user, db)
        materials = db.query(Material).filter(Material.company_id == company_id).offset(skip).limit(limit).all()
    else:
        from app.models.workspace import UserCompany
        user_companies = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).all()
        company_ids = [uc.company_id for uc in user_companies]
        materials = db.query(Material).filter(Material.company_id.in_(company_ids)).offset(skip).limit(limit).all()
    
    return materials

@router.post("/", response_model=MaterialSchema)
def create_material(
    *,
    db: Session = Depends(deps.get_db),
    material_in: MaterialCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new material.
    """
    deps.get_workspace_access_sync(material_in.company_id, current_user, db)
    
    # Check if code already exists for this company
    existing = db.query(Material).filter(
        Material.company_id == material_in.company_id,
        Material.code == material_in.code
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="Material code already exists for this company")
    
    material = Material(**material_in.dict())
    db.add(material)
    db.commit()
    db.refresh(material)
    return material

@router.get("/{id}", response_model=MaterialSchema)
def read_material(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get material by ID.
    """
    material = db.query(Material).filter(Material.id == id).first()
    if not material:
        raise HTTPException(status_code=404, detail="Material not found")
    
    deps.get_workspace_access_sync(material.company_id, current_user, db)
    return material

@router.put("/{id}", response_model=MaterialSchema)
def update_material(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    material_in: MaterialUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a material.
    """
    material = db.query(Material).filter(Material.id == id).first()
    if not material:
        raise HTTPException(status_code=404, detail="Material not found")
    
    deps.get_workspace_access_sync(material.company_id, current_user, db)
    
    for field, value in material_in.dict(exclude_unset=True).items():
        setattr(material, field, value)
    
    db.add(material)
    db.commit()
    db.refresh(material)
    return material

@router.get("/categories/", response_model=List[MaterialCategorySchema])
def read_material_categories(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve material categories.
    """
    categories = db.query(MaterialCategory).offset(skip).limit(limit).all()
    return categories

@router.post("/categories/", response_model=MaterialCategorySchema)
def create_material_category(
    *,
    db: Session = Depends(deps.get_db),
    category_in: MaterialCategorySchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new material category.
    """
    # Check if code already exists
    existing = db.query(MaterialCategory).filter(MaterialCategory.code == category_in.code).first()
    if existing:
        raise HTTPException(status_code=400, detail="Category code already exists")
    
    category = MaterialCategory(**category_in.dict(exclude={'id'}))
    db.add(category)
    db.commit()
    db.refresh(category)
    return category

@router.post("/{id}/technical-sheets", response_model=TechnicalSheetSchema)
def create_technical_sheet(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    sheet_in: TechnicalSheetSchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create technical sheet for material.
    """
    material = db.query(Material).filter(Material.id == id).first()
    if not material:
        raise HTTPException(status_code=404, detail="Material not found")
    
    deps.get_workspace_access_sync(material.company_id, current_user, db)
    
    sheet = TechnicalSheet(**sheet_in.dict(exclude={'id'}), material_id=id)
    db.add(sheet)
    db.commit()
    db.refresh(sheet)
    return sheet

@router.get("/{id}/price-history", response_model=List[PriceHistorySchema])
def read_material_price_history(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get material price history.
    """
    material = db.query(Material).filter(Material.id == id).first()
    if not material:
        raise HTTPException(status_code=404, detail="Material not found")
    
    deps.get_workspace_access_sync(material.company_id, current_user, db)
    
    price_history = db.query(PriceHistory).filter(PriceHistory.material_id == id).all()
    return price_history

@router.post("/{id}/price-history", response_model=PriceHistorySchema)
def create_price_history(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    price_in: PriceHistorySchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create price history entry for material.
    """
    material = db.query(Material).filter(Material.id == id).first()
    if not material:
        raise HTTPException(status_code=404, detail="Material not found")
    
    deps.get_workspace_access_sync(material.company_id, current_user, db)
    
    price_entry = PriceHistory(**price_in.dict(exclude={'id'}), material_id=id)
    db.add(price_entry)
    
    # Update current price
    material.current_price = price_in.price
    db.add(material)
    
    db.commit()
    db.refresh(price_entry)
    return price_entry
