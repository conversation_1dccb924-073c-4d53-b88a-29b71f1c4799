# app/schemas/document.py
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class DocumentBase(BaseModel):
    name: Optional[str] = None
    original_name: Optional[str] = None
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    mime_type: Optional[str] = None
    category: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = True

class DocumentCreate(DocumentBase):
    name: str
    original_name: str
    file_path: str
    company_id: int

class DocumentUpdate(DocumentBase):
    pass

class DocumentInDBBase(DocumentBase):
    id: Optional[int] = None
    company_id: Optional[int] = None
    uploaded_by: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Document(DocumentInDBBase):
    pass

class DocumentVersion(BaseModel):
    id: Optional[int] = None
    document_id: int
    version_number: str
    file_path: str
    file_size: Optional[int] = None
    uploaded_by: Optional[int] = None
    changes_description: Optional[str] = None

    class Config:
        from_attributes = True

class DocumentFolder(BaseModel):
    id: Optional[int] = None
    document_id: int
    folder_name: str
    folder_path: str
    parent_id: Optional[int] = None

    class Config:
        from_attributes = True