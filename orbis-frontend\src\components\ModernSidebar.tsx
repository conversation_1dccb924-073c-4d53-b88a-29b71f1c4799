'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname, useSearchParams } from 'next/navigation'
import {
  HomeIcon,
  FolderIcon,
  UsersIcon,
  ChartBarIcon,
  DocumentTextIcon,
  CogIcon,
  MagnifyingGlassIcon,
  Bars3Icon,
  BuildingOfficeIcon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'

interface SubMenuItem {
  name: string
  href: string
  icon: string
}

interface SidebarItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: number
  subItems?: SubMenuItem[]
}

const navigation: SidebarItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Carnet d\'adresses', href: '/entreprises', icon: BuildingOfficeIcon },
  {
    name: 'Dossiers',
    href: '/projects',
    icon: FolderIcon,
    badge: 3,
    subItems: [
      {
        name: '<PERSON><PERSON>',
        href: '/projects?filter=devis',
        icon: '📋'
      },
      {
        name: 'AO',
        href: '/projects?filter=ao',
        icon: '📢'
      },
      {
        name: 'Affaires',
        href: '/projects?filter=affaires',
        icon: '🤝'
      }
    ]
  },
  { name: 'Équipe', href: '/employees', icon: UsersIcon },
  { name: 'Rapports', href: '/reports', icon: ChartBarIcon },
  { name: 'Paramètres', href: '/settings', icon: CogIcon },
]

interface ModernSidebarProps {
  user?: {
    name: string
    email: string
    avatar?: string
  }
}

export default function ModernSidebar({ user }: ModernSidebarProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>(['Dossiers']) // Dossiers ouvert par défaut
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="p-2 rounded-lg bg-white shadow-lg border border-gray-200 hover:bg-gray-50 transition-colors"
        >
          {isOpen ? (
            <XMarkIcon className="h-6 w-6 text-gray-600" />
          ) : (
            <Bars3Icon className="h-6 w-6 text-gray-600" />
          )}
        </button>
      </div>

      {/* Overlay for mobile */}
      {isOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed left-0 top-0 z-40 h-screen w-72 bg-white border-r border-gray-200 shadow-lg
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-cyan-600 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">O</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">ORBIS</h1>
                <p className="text-xs text-primary-600 font-medium">SUIVI TRAVAUX</p>
              </div>
            </div>
          </div>

          {/* Search */}
          <div className="p-4">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher..."
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 space-y-2 pb-4">
            {navigation.map((item) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
              const isExpanded = expandedItems.includes(item.name)
              const hasSubItems = item.subItems && item.subItems.length > 0

              return (
                <div key={item.name}>
                  {hasSubItems ? (
                    <button
                      onClick={() => toggleExpanded(item.name)}
                      className={`sidebar-item w-full justify-between ${isActive ? 'active' : ''}`}
                    >
                      <div className="flex items-center space-x-3">
                        <item.icon className={`h-5 w-5 ${isActive ? 'text-primary-600' : 'text-gray-500'}`} />
                        <span className="font-medium">{item.name}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        {item.badge && (
                          <span className={`
                            px-2 py-1 text-xs font-medium rounded-full
                            ${isActive
                              ? 'bg-primary-100 text-primary-700'
                              : 'bg-primary-100 text-primary-600'
                            }
                          `}>
                            {item.badge}
                          </span>
                        )}
                        {isExpanded ? (
                          <ChevronDownIcon className={`h-4 w-4 ${isActive ? 'text-primary-600' : 'text-gray-500'}`} />
                        ) : (
                          <ChevronRightIcon className={`h-4 w-4 ${isActive ? 'text-primary-600' : 'text-gray-500'}`} />
                        )}
                      </div>
                    </button>
                  ) : (
                    <Link
                      href={item.href}
                      className={`sidebar-item justify-between ${isActive ? 'active' : ''}`}
                      onClick={() => setIsOpen(false)}
                    >
                      <div className="flex items-center space-x-3">
                        <item.icon className={`h-5 w-5 ${isActive ? 'text-primary-600' : 'text-gray-500'}`} />
                        <span className="font-medium">{item.name}</span>
                      </div>
                      {item.badge && (
                        <span className={`
                          px-2 py-1 text-xs font-medium rounded-full
                          ${isActive
                            ? 'bg-primary-100 text-primary-700'
                            : 'bg-primary-100 text-primary-600'
                          }
                        `}>
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  )}

                  {/* Sous-menus */}
                  {hasSubItems && isExpanded && (
                    <div className="ml-6 mt-2 space-y-1">
                      {item.subItems.map((subItem) => {
                        const subIsActive = pathname.includes(subItem.href) ||
                          (subItem.href.includes('filter=devis') && pathname.includes('projects') && searchParams?.get('filter') === 'devis') ||
                          (subItem.href.includes('filter=ao') && pathname.includes('projects') && searchParams?.get('filter') === 'ao') ||
                          (subItem.href.includes('filter=affaires') && pathname.includes('projects') && searchParams?.get('filter') === 'affaires')

                        return (
                          <Link
                            key={subItem.name}
                            href={subItem.href}
                            className={`
                              flex items-center space-x-3 px-4 py-2 rounded-lg transition-all duration-200
                              ${subIsActive
                                ? 'bg-primary-50 text-primary-700 border-l-2 border-primary-500'
                                : 'text-gray-600 hover:bg-gray-50 hover:text-primary-600'
                              }
                            `}
                            onClick={() => setIsOpen(false)}
                          >
                            <span className="text-sm">{subItem.icon}</span>
                            <span className="text-sm font-medium">{subItem.name}</span>
                          </Link>
                        )
                      })}
                    </div>
                  )}
                </div>
              )
            })}
          </nav>
        </div>
      </div>
    </>
  )
}
