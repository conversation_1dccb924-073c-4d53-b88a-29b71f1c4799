# app/api/api_v1/endpoints/workspaces.py
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.models.workspace import Workspace, UserWorkspace, WorkspaceSettings
from app.schemas.workspace import (
    Workspace as WorkspaceSchema, 
    WorkspaceCreate, 
    WorkspaceUpdate, 
    WorkspaceSettings as WorkspaceSettingsSchema,
    WorkspaceWithUsers,
    UserWorkspace as UserWorkspaceSchema,
    UserWorkspaceCreate
)

router = APIRouter()

@router.get("/", response_model=List[WorkspaceSchema])
def read_workspaces(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve workspaces accessible by current user.
    """
    user_workspaces = db.query(UserWorkspace).filter(UserWorkspace.user_id == current_user.id).all()
    workspace_ids = [uw.workspace_id for uw in user_workspaces]
    workspaces = db.query(Workspace).filter(Workspace.id.in_(workspace_ids)).offset(skip).limit(limit).all()
    return workspaces

@router.post("/", response_model=WorkspaceSchema)
def create_workspace(
    *,
    db: Session = Depends(deps.get_db),
    workspace_in: WorkspaceCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new workspace.
    """
    # Check if code already exists
    if db.query(Workspace).filter(Workspace.code == workspace_in.code).first():
        raise HTTPException(status_code=400, detail="Workspace code already exists")
    
    # Check workspace limit
    user_workspace_count = db.query(UserWorkspace).filter(UserWorkspace.user_id == current_user.id).count()
    if user_workspace_count >= 10:
        raise HTTPException(status_code=400, detail="Maximum number of workspaces reached (10)")
    
    workspace = Workspace(**workspace_in.dict())
    db.add(workspace)
    db.commit()
    db.refresh(workspace)
    
    # Add current user as admin of the new workspace
    user_workspace = UserWorkspace(
        user_id=current_user.id,
        workspace_id=workspace.id,
        role_name="ADMIN",
        is_default=user_workspace_count == 0,  # First workspace is default
        is_active=True
    )
    db.add(user_workspace)
    
    # Create default settings for the workspace
    workspace_settings = WorkspaceSettings(
        workspace_id=workspace.id,
        default_currency="EUR",
        language="fr"
    )
    db.add(workspace_settings)
    
    db.commit()
    return workspace

@router.get("/{workspace_id}", response_model=WorkspaceSchema)
def read_workspace(
    *,
    db: Session = Depends(deps.get_db),
    workspace_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get workspace by ID.
    """
    # Check if user has access to this workspace
    user_workspace = db.query(UserWorkspace).filter(
        UserWorkspace.user_id == current_user.id,
        UserWorkspace.workspace_id == workspace_id,
        UserWorkspace.is_active == True
    ).first()
    
    if not user_workspace:
        raise HTTPException(status_code=404, detail="Workspace not found")
    
    workspace = db.query(Workspace).filter(Workspace.id == workspace_id).first()
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace not found")
    
    return workspace

@router.put("/{workspace_id}", response_model=WorkspaceSchema)
def update_workspace(
    *,
    db: Session = Depends(deps.get_db),
    workspace_id: int,
    workspace_in: WorkspaceUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a workspace.
    """
    # Check if user has admin access to this workspace
    user_workspace = db.query(UserWorkspace).filter(
        UserWorkspace.user_id == current_user.id,
        UserWorkspace.workspace_id == workspace_id,
        UserWorkspace.role_name.in_(["ADMIN", "MANAGER"]),
        UserWorkspace.is_active == True
    ).first()
    
    if not user_workspace:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    workspace = db.query(Workspace).filter(Workspace.id == workspace_id).first()
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace not found")
    
    # Check if new code already exists (if provided)
    if workspace_in.code and workspace_in.code != workspace.code:
        if db.query(Workspace).filter(Workspace.code == workspace_in.code).first():
            raise HTTPException(status_code=400, detail="Workspace code already exists")
    
    update_data = workspace_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(workspace, field, value)
    
    db.add(workspace)
    db.commit()
    db.refresh(workspace)
    return workspace

@router.delete("/{workspace_id}")
def delete_workspace(
    *,
    db: Session = Depends(deps.get_db),
    workspace_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a workspace.
    """
    # Check if user is admin of this workspace
    user_workspace = db.query(UserWorkspace).filter(
        UserWorkspace.user_id == current_user.id,
        UserWorkspace.workspace_id == workspace_id,
        UserWorkspace.role_name == "ADMIN",
        UserWorkspace.is_active == True
    ).first()
    
    if not user_workspace:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    workspace = db.query(Workspace).filter(Workspace.id == workspace_id).first()
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace not found")
    
    # Soft delete by setting is_active to False
    workspace.is_active = False
    db.add(workspace)
    db.commit()
    
    return {"message": "Workspace deleted successfully"}

@router.get("/{workspace_id}/users", response_model=List[UserWorkspaceSchema])
def read_workspace_users(
    *,
    db: Session = Depends(deps.get_db),
    workspace_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get all users in a workspace.
    """
    # Check if user has access to this workspace
    user_workspace = db.query(UserWorkspace).filter(
        UserWorkspace.user_id == current_user.id,
        UserWorkspace.workspace_id == workspace_id,
        UserWorkspace.is_active == True
    ).first()
    
    if not user_workspace:
        raise HTTPException(status_code=404, detail="Workspace not found")
    
    workspace_users = db.query(UserWorkspace).filter(
        UserWorkspace.workspace_id == workspace_id,
        UserWorkspace.is_active == True
    ).all()
    
    return workspace_users

@router.post("/{workspace_id}/users", response_model=UserWorkspaceSchema)
def add_user_to_workspace(
    *,
    db: Session = Depends(deps.get_db),
    workspace_id: int,
    user_workspace_in: UserWorkspaceCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Add a user to a workspace.
    """
    # Check if current user is admin/manager of this workspace
    current_user_workspace = db.query(UserWorkspace).filter(
        UserWorkspace.user_id == current_user.id,
        UserWorkspace.workspace_id == workspace_id,
        UserWorkspace.role_name.in_(["ADMIN", "MANAGER"]),
        UserWorkspace.is_active == True
    ).first()
    
    if not current_user_workspace:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    # Check if user is already in workspace
    existing = db.query(UserWorkspace).filter(
        UserWorkspace.user_id == user_workspace_in.user_id,
        UserWorkspace.workspace_id == workspace_id
    ).first()
    
    if existing:
        if existing.is_active:
            raise HTTPException(status_code=400, detail="User already in workspace")
        else:
            # Reactivate existing relationship
            existing.is_active = True
            existing.role_name = user_workspace_in.role_name
            db.add(existing)
            db.commit()
            db.refresh(existing)
            return existing
    
    # Create new user-workspace relationship
    user_workspace = UserWorkspace(
        user_id=user_workspace_in.user_id,
        workspace_id=workspace_id,
        role_name=user_workspace_in.role_name,
        invited_by=current_user.id,
        is_active=True
    )
    db.add(user_workspace)
    db.commit()
    db.refresh(user_workspace)
    
    return user_workspace

@router.get("/{workspace_id}/settings", response_model=WorkspaceSettingsSchema)
def read_workspace_settings(
    *,
    db: Session = Depends(deps.get_db),
    workspace_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get workspace settings.
    """
    # Check if user has access to this workspace
    user_workspace = db.query(UserWorkspace).filter(
        UserWorkspace.user_id == current_user.id,
        UserWorkspace.workspace_id == workspace_id,
        UserWorkspace.is_active == True
    ).first()
    
    if not user_workspace:
        raise HTTPException(status_code=404, detail="Workspace not found")
    
    settings = db.query(WorkspaceSettings).filter(
        WorkspaceSettings.workspace_id == workspace_id
    ).first()
    
    if not settings:
        # Create default settings if they don't exist
        settings = WorkspaceSettings(
            workspace_id=workspace_id,
            default_currency="EUR",
            language="fr"
        )
        db.add(settings)
        db.commit()
        db.refresh(settings)
    
    return settings
