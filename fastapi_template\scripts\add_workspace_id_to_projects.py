#!/usr/bin/env python3
"""
Script pour ajouter la colonne workspace_id à la table projects
"""

import asyncio
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.database import engine

async def add_workspace_id_to_projects():
    """Ajoute la colonne workspace_id à la table projects"""
    
    try:
        async with engine.begin() as conn:
            print("🔄 Ajout de la colonne workspace_id à la table projects...")
            
            # Vérifier si la colonne existe déjà
            check_column_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'projects' 
                AND column_name = 'workspace_id'
            """)
            
            result = await conn.execute(check_column_query)
            existing_column = result.fetchone()
            
            if not existing_column:
                # Ajouter la colonne workspace_id (nullable temporairement)
                await conn.execute(text("""
                    ALTER TABLE projects 
                    ADD COLUMN workspace_id INTEGER
                """))
                print("✅ Colonne workspace_id ajoutée")
                
                # Mettre à jour tous les projets existants avec un workspace_id par défaut
                # On va utiliser le premier workspace disponible
                print("🔄 Mise à jour des projets existants...")
                
                # Récupérer le premier workspace
                workspace_result = await conn.execute(text("""
                    SELECT id FROM workspaces ORDER BY id LIMIT 1
                """))
                first_workspace = workspace_result.fetchone()
                
                if first_workspace:
                    workspace_id = first_workspace[0]
                    await conn.execute(text("""
                        UPDATE projects 
                        SET workspace_id = :workspace_id 
                        WHERE workspace_id IS NULL
                    """), {"workspace_id": workspace_id})
                    print(f"✅ Projets mis à jour avec workspace_id = {workspace_id}")
                    
                    # Maintenant rendre la colonne NOT NULL
                    await conn.execute(text("""
                        ALTER TABLE projects 
                        ALTER COLUMN workspace_id SET NOT NULL
                    """))
                    print("✅ Colonne workspace_id définie comme NOT NULL")
                    
                    # Ajouter la contrainte de clé étrangère
                    await conn.execute(text("""
                        ALTER TABLE projects 
                        ADD CONSTRAINT fk_projects_workspace_id 
                        FOREIGN KEY (workspace_id) REFERENCES workspaces(id)
                    """))
                    print("✅ Contrainte de clé étrangère ajoutée")
                else:
                    print("❌ Aucun workspace trouvé pour la migration")
                    return
                    
            else:
                print("ℹ️  Colonne workspace_id existe déjà")
            
            print("🎉 Migration terminée avec succès!")
            
    except Exception as e:
        print(f"❌ Erreur lors de la migration: {e}")
        raise
    finally:
        await engine.dispose()

if __name__ == "__main__":
    asyncio.run(add_workspace_id_to_projects())
