# app/models/document.py
from sqlalchemy import <PERSON><PERSON>an, <PERSON>umn, Integer, String, DateTime, ForeignKey, Text, BigInteger, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.core.database import Base

class DocumentType(str, enum.Enum):
    """Types de documents techniques"""
    CCTP = "CCTP"  # Cahier des Clauses Techniques Particulières
    DPGF = "DPGF"  # Décomposition du Prix Global et Forfaitaire

class Document(Base):
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id"), nullable=False)
    name = Column(String, nullable=False, index=True)
    original_name = Column(String, nullable=False)
    file_path = Column(String, nullable=False)
    file_size = Column(BigInteger)
    mime_type = Column(String)
    category = Column(String)  # DCE, DAO, DPGF, FT, EXE, PLANNING, etc.
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    uploaded_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    workspace = relationship("Workspace", back_populates="documents")
    uploader = relationship("User")
    versions = relationship("DocumentVersion", back_populates="document")
    folders = relationship("DocumentFolder", back_populates="document")

class DocumentVersion(Base):
    __tablename__ = "document_versions"

    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    version_number = Column(String, nullable=False)
    file_path = Column(String, nullable=False)
    file_size = Column(BigInteger)
    uploaded_by = Column(Integer, ForeignKey("users.id"))
    changes_description = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    document = relationship("Document", back_populates="versions")
    uploader = relationship("User")

class DocumentFolder(Base):
    __tablename__ = "document_folders"

    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    folder_name = Column(String, nullable=False)
    folder_path = Column(String, nullable=False)
    parent_id = Column(Integer, ForeignKey("document_folders.id"))
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    document = relationship("Document", back_populates="folders")
    parent = relationship("DocumentFolder", remote_side=[id])

class TechnicalDocument(Base):
    """Table pour les documents techniques éditables (CCTP, DPGF)"""
    __tablename__ = "technical_documents"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    type_document = Column(Enum(DocumentType), nullable=False)
    content = Column(Text)  # Contenu HTML du document
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    updated_by = Column(Integer, ForeignKey("users.id"))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    project = relationship("Project")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    companies = relationship("TechnicalDocumentCompany", back_populates="technical_document", cascade="all, delete-orphan")

class TechnicalDocumentCompany(Base):
    """Table de relation many-to-many entre documents techniques et companies"""
    __tablename__ = "technical_document_companies"

    id = Column(Integer, primary_key=True, index=True)
    technical_document_id = Column(Integer, ForeignKey("technical_documents.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    technical_document = relationship("TechnicalDocument", back_populates="companies")
    company = relationship("Workspace")

    # Contrainte d'unicité pour éviter les doublons
    __table_args__ = (
        {"extend_existing": True}
    )