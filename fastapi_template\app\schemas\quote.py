# app/schemas/quote.py
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from decimal import Decimal
from app.models.quote import QuoteStatus

class QuoteBase(BaseModel):
    quote_number: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    quote_date: Optional[datetime] = None
    expiry_date: Optional[datetime] = None
    total_amount_ht: Optional[Decimal] = None
    vat_amount: Optional[Decimal] = None
    total_amount_ttc: Optional[Decimal] = None
    status: Optional[QuoteStatus] = QuoteStatus.DRAFT
    notes: Optional[str] = None

class QuoteCreate(QuoteBase):
    quote_number: str
    title: str
    quote_date: datetime
    company_id: int

class QuoteUpdate(QuoteBase):
    pass

class QuoteInDBBase(QuoteBase):
    id: Optional[int] = None
    company_id: Optional[int] = None
    project_id: Optional[int] = None
    supplier_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Quote(QuoteInDBBase):
    pass

class QuoteLine(BaseModel):
    id: Optional[int] = None
    quote_id: int
    material_id: Optional[int] = None
    description: str
    quantity: Decimal
    unit: str
    unit_price: Decimal
    total_amount: Decimal

    class Config:
        from_attributes = True

class QuoteTemplate(BaseModel):
    id: Optional[int] = None
    company_id: int
    name: str
    description: Optional[str] = None
    template_data: Optional[str] = None
    is_active: Optional[bool] = True

    class Config:
        from_attributes = True