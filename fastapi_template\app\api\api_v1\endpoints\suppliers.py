# app/api/api_v1/endpoints/suppliers.py
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.models.supplier import Supplier, SupplierContact
from app.schemas.supplier import Supplier as SupplierSchema, SupplierCreate, SupplierUpdate, SupplierContact as SupplierContactSchema

router = APIRouter()

@router.get("/", response_model=List[SupplierSchema])
def read_suppliers(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    company_id: int = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve suppliers.
    """
    if company_id:
        deps.get_workspace_access_sync(company_id, current_user, db)
        suppliers = db.query(Supplier).filter(Supplier.company_id == company_id).offset(skip).limit(limit).all()
    else:
        from app.models.workspace import UserCompany
        user_companies = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).all()
        company_ids = [uc.company_id for uc in user_companies]
        suppliers = db.query(Supplier).filter(Supplier.company_id.in_(company_ids)).offset(skip).limit(limit).all()
    
    return suppliers

@router.post("/", response_model=SupplierSchema)
def create_supplier(
    *,
    db: Session = Depends(deps.get_db),
    supplier_in: SupplierCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new supplier.
    """
    deps.get_company_access(supplier_in.company_id, current_user, db)
    
    # Check if code already exists for this company
    existing = db.query(Supplier).filter(
        Supplier.company_id == supplier_in.company_id,
        Supplier.code == supplier_in.code
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="Supplier code already exists for this company")
    
    supplier = Supplier(**supplier_in.dict())
    db.add(supplier)
    db.commit()
    db.refresh(supplier)
    return supplier

@router.get("/{id}", response_model=SupplierSchema)
def read_supplier(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get supplier by ID.
    """
    supplier = db.query(Supplier).filter(Supplier.id == id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="Supplier not found")
    
    deps.get_company_access(supplier.company_id, current_user, db)
    return supplier

@router.put("/{id}", response_model=SupplierSchema)
def update_supplier(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    supplier_in: SupplierUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a supplier.
    """
    supplier = db.query(Supplier).filter(Supplier.id == id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="Supplier not found")
    
    deps.get_company_access(supplier.company_id, current_user, db)
    
    for field, value in supplier_in.dict(exclude_unset=True).items():
        setattr(supplier, field, value)
    
    db.add(supplier)
    db.commit()
    db.refresh(supplier)
    return supplier

@router.post("/{id}/contacts", response_model=SupplierContactSchema)
def create_supplier_contact(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    contact_in: SupplierContactSchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create supplier contact.
    """
    supplier = db.query(Supplier).filter(Supplier.id == id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="Supplier not found")
    
    deps.get_company_access(supplier.company_id, current_user, db)
    
    contact = SupplierContact(**contact_in.dict(exclude={'id'}), supplier_id=id)
    db.add(contact)
    db.commit()
    db.refresh(contact)
    return contact

@router.get("/{id}/contacts", response_model=List[SupplierContactSchema])
def read_supplier_contacts(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get supplier contacts.
    """
    supplier = db.query(Supplier).filter(Supplier.id == id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="Supplier not found")
    
    deps.get_company_access(supplier.company_id, current_user, db)
    
    contacts = db.query(SupplierContact).filter(SupplierContact.supplier_id == id).all()
    return contacts