# app/api/deps.py
"""
Dépendances FastAPI pour l'authentification et l'autorisation
"""

from typing import Optional, Dict, Any
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db
from app.models.user import User
from app.models.workspace import Workspace, UserWorkspace
from app.middleware.rbac_middleware import (
    RBACMiddleware, require_super_admin as rbac_require_super_admin,
    require_company_permission, require_any_permission, get_user_context
)
from app.services.rbac_service import RBACService

reusable_oauth2 = HTTPBearer()

async def get_current_token(
    token: HTTPAuthorizationCredentials = Depends(reusable_oauth2)
) -> str:
    """Extract the current token string"""
    return token.credentials

async def get_current_user(
    db: AsyncSession = Depends(get_db), 
    token: HTTPAuthorizationCredentials = Depends(reusable_oauth2)
) -> User:
    """Get current user from JWT token"""
    try:
        # Use the enhanced token verification
        payload = security.verify_token(token.credentials, token_type="access")
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Could not validate credentials",
            )
        
        token_data = TokenPayload(**payload)
        
    except (jwt.JWTError, ValidationError, Exception):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )
    
    if not token_data.sub:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid token payload"
        )
    
    result = await db.execute(select(User).where(User.id == token_data.sub))
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    return user

async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

async def get_current_active_superuser(
    current_user: User = Depends(get_current_user),
) -> User:
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=400, detail="The user doesn't have enough privileges"
        )
    return current_user

def require_permissions(required_permissions: List[str]):
    """Dependency factory for permission-based access control"""
    async def permission_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        user_permissions = security.get_user_permissions(current_user.role)
        
        # Check if user has all required permissions
        missing_permissions = [perm for perm in required_permissions if perm not in user_permissions]
        
        if missing_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required permissions: {', '.join(missing_permissions)}"
            )
        
        return current_user
    
    return permission_checker

def require_role(required_roles: List[UserRole]):
    """Dependency factory for role-based access control"""
    async def role_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if current_user.role not in required_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: {[role.value for role in required_roles]}"
            )
        
        return current_user
    
    return role_checker

# Common permission dependencies
get_admin_user = require_role([UserRole.SUPER_ADMIN, UserRole.ADMIN])
get_manager_user = require_role([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.CHEF_PROJET])
get_user_manager = require_permissions(['manage_users'])
get_project_manager = require_permissions(['manage_projects'])

async def get_workspace_access(
    workspace_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> "Workspace":
    """Verify user has access to the specified workspace"""
    from app.models.workspace import UserWorkspace, Workspace

    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == current_user.id,
            UserWorkspace.workspace_id == workspace_id,
            UserWorkspace.is_active == True
        )
    )
    user_workspace = result.scalar_one_or_none()

    if not user_workspace:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this workspace"
        )

    result = await db.execute(select(Workspace).where(Workspace.id == workspace_id))
    workspace = result.scalar_one_or_none()
    if not workspace or not workspace.is_active:
        raise HTTPException(status_code=404, detail="Workspace not found")

    return workspace

# Alias de compatibilité
async def get_company_access(
    company_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> "Company":
    """Verify user has access to the specified company (compatibility alias)"""
    from app.models.workspace import UserWorkspace, Workspace

    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == current_user.id,
            UserWorkspace.workspace_id == company_id,
            UserWorkspace.is_active == True
        )
    )
    user_workspace = result.scalar_one_or_none()

    if not user_workspace:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this company"
        )

    result = await db.execute(select(Workspace).where(Workspace.id == company_id))
    workspace = result.scalar_one_or_none()
    if not workspace or not workspace.is_active:
        raise HTTPException(status_code=404, detail="Company not found")

    return workspace


# New tenant-aware dependencies
def get_current_tenant_company(request: Request) -> Company:
    """Get current company from tenant middleware"""
    return require_company_access(request)


def require_admin_role(request: Request) -> str:
    """Require admin role in current company"""
    return require_company_role(request, [CompanyRole.ADMIN])


def require_manager_role(request: Request) -> str:
    """Require manager or admin role in current company"""
    return require_company_role(request, [CompanyRole.ADMIN, CompanyRole.MANAGER])


def require_user_role(request: Request) -> str:
    """Require user, manager or admin role in current company"""
    return require_company_role(request, [CompanyRole.ADMIN, CompanyRole.MANAGER, CompanyRole.USER])


async def get_user_companies(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> List[UserCompany]:
    """Get all companies for current user"""
    result = await db.execute(
        select(UserCompany, Company)
        .join(Company, UserCompany.company_id == Company.id)
        .where(
            UserCompany.user_id == current_user.id,
            UserCompany.is_active == True,
            Company.is_active == True
        )
        .order_by(UserCompany.is_default.desc(), Company.name.asc())
    )
    return [uc for uc, _ in result.all()]

# ===== NOUVELLES DÉPENDANCES RBAC =====

# Dépendances pour les permissions système
async def require_super_admin_new(
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """Nouvelle dépendance pour exiger un rôle super admin"""
    return await rbac_require_super_admin(request, db)

# Dépendances pour les permissions d'entreprise
def require_projects_read():
    """Dépendance pour la permission de lecture des projets"""
    return require_company_permission("projects.read")

def require_projects_create():
    """Dépendance pour la permission de création des projets"""
    return require_company_permission("projects.create")

def require_projects_update():
    """Dépendance pour la permission de modification des projets"""
    return require_company_permission("projects.update")

def require_projects_delete():
    """Dépendance pour la permission de suppression des projets"""
    return require_company_permission("projects.delete")

def require_users_manage():
    """Dépendance pour la gestion des utilisateurs"""
    return require_company_permission("users.manage_roles")

def require_documents_read():
    """Dépendance pour la lecture des documents"""
    return require_company_permission("documents.read")

def require_documents_upload():
    """Dépendance pour l'upload des documents"""
    return require_company_permission("documents.upload")

def require_budgets_read():
    """Dépendance pour la lecture des budgets"""
    return require_company_permission("budgets.read")

def require_budgets_approve():
    """Dépendance pour l'approbation des budgets"""
    return require_company_permission("budgets.approve")

# Dépendances pour permissions multiples
def require_project_manager():
    """Dépendance pour les gestionnaires de projets"""
    return require_any_permission([
        "projects.create", "projects.update", "projects.manage_team"
    ])

def require_financial_access():
    """Dépendance pour l'accès aux données financières"""
    return require_any_permission([
        "budgets.read", "invoices.read", "quotes.read", "projects.view_financial"
    ])

# Dépendances pour le contexte utilisateur
async def get_current_user_rbac(
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """Récupère l'utilisateur actuel avec le contexte RBAC"""
    return await get_user_context(request, db)

async def get_rbac_service(db: AsyncSession = Depends(get_db)) -> RBACService:
    """Fournit une instance du service RBAC"""
    return RBACService(db)

# Fonction utilitaire pour vérifier les permissions
async def check_user_permission(
    user_id: int,
    company_id: int,
    permission: str,
    db: AsyncSession = Depends(get_db)
) -> bool:
    """Vérifie si un utilisateur a une permission spécifique"""
    rbac_service = RBACService(db)
    return await rbac_service.check_permission(user_id, company_id, permission)