# app/api/api_v1/endpoints/excel.py
from typing import Any, List, Dict
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
import pandas as pd
import io
import os
from datetime import datetime

from app.api import deps
from app.models.user import User
from app.models.project import Project
from app.models.material import Material
from app.models.financial import Budget, BudgetLine
from app.models.purchase_order import PurchaseOrder, PurchaseOrderLine
from app.core.config import settings

router = APIRouter()

@router.post("/import-dpgf")
async def import_dpgf(
    *,
    db: Session = Depends(deps.get_db),
    workspace_id: int = Form(...),
    project_id: int = Form(...),
    file: UploadFile = File(...),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Import DPGF (Price Schedule) from Excel file.
    """
    deps.get_workspace_access_sync(company_id, current_user, db)
    
    # Verify project exists and belongs to company
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.workspace_id == company_id
    ).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    # Validate file type
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="File must be Excel format (.xlsx or .xls)")
    
    try:
        # Read Excel file
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))
        
        # Expected columns for DPGF import
        required_columns = ['description', 'unit', 'quantity', 'unit_price']
        optional_columns = ['total_ht', 'purchase_budget', 'hours', 'hourly_rate', 'cctp_index', 'sales_coefficient']
        
        # Check if required columns exist (case insensitive)
        df_columns_lower = [col.lower().strip() for col in df.columns]
        missing_columns = []
        
        column_mapping = {}
        for req_col in required_columns:
            found = False
            for i, df_col in enumerate(df_columns_lower):
                if req_col.lower() in df_col or df_col in req_col.lower():
                    column_mapping[req_col] = df.columns[i]
                    found = True
                    break
            if not found:
                missing_columns.append(req_col)
        
        if missing_columns:
            return {
                "error": f"Missing required columns: {', '.join(missing_columns)}",
                "available_columns": list(df.columns),
                "suggested_mapping": {
                    "description": "Work description / Description des travaux",
                    "unit": "Unit / Unité",
                    "quantity": "Quantity / Quantité",
                    "unit_price": "Unit Price / Prix unitaire"
                }
            }
        
        # Map optional columns
        for opt_col in optional_columns:
            for i, df_col in enumerate(df_columns_lower):
                if opt_col.lower() in df_col or df_col in opt_col.lower():
                    column_mapping[opt_col] = df.columns[i]
                    break
        
        # Process data
        imported_items = []
        errors = []
        
        for index, row in df.iterrows():
            try:
                # Extract required data
                description = str(row[column_mapping['description']]).strip()
                unit = str(row[column_mapping['unit']]).strip()
                quantity = float(row[column_mapping['quantity']])
                unit_price = float(row[column_mapping['unit_price']])
                
                if not description or description == 'nan':
                    continue  # Skip empty rows
                
                # Calculate total if not provided
                total_ht = quantity * unit_price
                if 'total_ht' in column_mapping:
                    try:
                        total_ht = float(row[column_mapping['total_ht']])
                    except:
                        pass
                
                # Create material if it doesn't exist
                material_code = f"MAT-{project.code}-{index + 1:04d}"
                material = db.query(Material).filter(
                    Material.workspace_id == company_id,
                    Material.code == material_code
                ).first()
                
                if not material:
                    material = Material(
                        workspace_id =company_id,
                        name=description[:100],  # Limit name length
                        code=material_code,
                        description=description,
                        unit=unit,
                        current_price=unit_price,
                        is_active=True
                    )
                    db.add(material)
                    db.flush()  # Get the ID
                
                # Create budget line
                budget_line_data = {
                    'description': description,
                    'category': 'DPGF Import',
                    'quantity': quantity,
                    'unit_price': unit_price,
                    'total_amount': total_ht
                }
                
                # Add optional fields
                if 'purchase_budget' in column_mapping:
                    try:
                        budget_line_data['purchase_budget'] = float(row[column_mapping['purchase_budget']])
                    except:
                        pass
                
                if 'hours' in column_mapping:
                    try:
                        budget_line_data['hours'] = float(row[column_mapping['hours']])
                    except:
                        pass
                
                if 'hourly_rate' in column_mapping:
                    try:
                        budget_line_data['hourly_rate'] = float(row[column_mapping['hourly_rate']])
                    except:
                        pass
                
                imported_items.append({
                    'row': index + 1,
                    'material_id': material.id,
                    'description': description,
                    'unit': unit,
                    'quantity': quantity,
                    'unit_price': unit_price,
                    'total_ht': total_ht,
                    'budget_line_data': budget_line_data
                })
                
            except Exception as e:
                errors.append({
                    'row': index + 1,
                    'error': str(e),
                    'data': dict(row)
                })
        
        # Create or update project budget
        budget = db.query(Budget).filter(
            Budget.project_id == project_id,
            Budget.name == f"DPGF Import - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        ).first()
        
        if not budget:
            total_budget = sum([item['total_ht'] for item in imported_items])
            budget = Budget(
                workspace_id =company_id,
                project_id=project_id,
                name=f"DPGF Import - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                description=f"Imported from {file.filename}",
                total_amount=total_budget,
                status='draft'
            )
            db.add(budget)
            db.flush()
        
        # Create budget lines
        for item in imported_items:
            budget_line = BudgetLine(
                budget_id=budget.id,
                **item['budget_line_data']
            )
            db.add(budget_line)
        
        db.commit()
        
        return {
            "success": True,
            "message": f"Successfully imported {len(imported_items)} items",
            "budget_id": budget.id,
            "imported_items": len(imported_items),
            "errors": errors,
            "summary": {
                "total_items": len(imported_items),
                "total_amount": sum([item['total_ht'] for item in imported_items]),
                "errors_count": len(errors)
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing Excel file: {str(e)}")

@router.get("/export-dpgf/{project_id}")
def export_dpgf(
    *,
    db: Session = Depends(deps.get_db),
    project_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Export project DPGF to Excel file.
    """
    # Get project and verify access
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    deps.get_workspace_access_sync(project.workspace_id, current_user, db)
    
    # Get project budgets and lines
    budgets = db.query(Budget).filter(Budget.project_id == project_id).all()
    
    export_data = []
    for budget in budgets:
        budget_lines = db.query(BudgetLine).filter(BudgetLine.budget_id == budget.id).all()
        
        for line in budget_lines:
            export_data.append({
                'Budget': budget.name,
                'Description': line.description,
                'Category': line.category,
                'Quantity': float(line.quantity) if line.quantity else 0,
                'Unit Price': float(line.unit_price) if line.unit_price else 0,
                'Total HT': float(line.total_amount) if line.total_amount else 0,
                'Created Date': line.created_at.strftime('%Y-%m-%d') if line.created_at else ''
            })
    
    # Create Excel file
    df = pd.DataFrame(export_data)
    
    # Create Excel file in memory
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='DPGF Export', index=False)
        
        # Get workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['DPGF Export']
        
        # Add formatting
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#D7E4BC',
            'border': 1
        })
        
        # Format header row
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
        
        # Auto-adjust column widths
        for i, col in enumerate(df.columns):
            max_length = max(
                df[col].astype(str).map(len).max(),
                len(col)
            )
            worksheet.set_column(i, i, min(max_length + 2, 50))
    
    output.seek(0)
    
    # Create filename
    filename = f"DPGF_{project.code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    return StreamingResponse(
        io.BytesIO(output.read()),
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

@router.post("/import-materials")
async def import_materials(
    *,
    db: Session = Depends(deps.get_db),
    workspace_id: int = Form(...),
    file: UploadFile = File(...),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Import materials catalog from Excel file.
    """
    deps.get_workspace_access_sync(company_id, current_user, db)
    
    # Validate file type
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="File must be Excel format (.xlsx or .xls)")
    
    try:
        # Read Excel file
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))
        
        # Expected columns for materials import
        required_columns = ['name', 'code', 'unit']
        optional_columns = ['description', 'current_price', 'minimum_quantity', 'maximum_quantity']
        
        # Check if required columns exist
        df_columns_lower = [col.lower().strip() for col in df.columns]
        missing_columns = []
        
        column_mapping = {}
        for req_col in required_columns:
            found = False
            for i, df_col in enumerate(df_columns_lower):
                if req_col.lower() in df_col or df_col in req_col.lower():
                    column_mapping[req_col] = df.columns[i]
                    found = True
                    break
            if not found:
                missing_columns.append(req_col)
        
        if missing_columns:
            return {
                "error": f"Missing required columns: {', '.join(missing_columns)}",
                "available_columns": list(df.columns)
            }
        
        # Map optional columns
        for opt_col in optional_columns:
            for i, df_col in enumerate(df_columns_lower):
                if opt_col.lower() in df_col or df_col in opt_col.lower():
                    column_mapping[opt_col] = df.columns[i]
                    break
        
        # Process data
        imported_materials = []
        errors = []
        
        for index, row in df.iterrows():
            try:
                name = str(row[column_mapping['name']]).strip()
                code = str(row[column_mapping['code']]).strip()
                unit = str(row[column_mapping['unit']]).strip()
                
                if not name or name == 'nan' or not code or code == 'nan':
                    continue  # Skip empty rows
                
                # Check if material already exists
                existing = db.query(Material).filter(
                    Material.workspace_id == company_id,
                    Material.code == code
                ).first()
                
                if existing:
                    errors.append({
                        'row': index + 1,
                        'error': f"Material with code '{code}' already exists",
                        'data': {'name': name, 'code': code}
                    })
                    continue
                
                # Create material
                material_data = {
                    'workspace_id': company_id,
                    'name': name,
                    'code': code,
                    'unit': unit,
                    'is_active': True
                }
                
                # Add optional fields
                if 'description' in column_mapping:
                    try:
                        description = str(row[column_mapping['description']]).strip()
                        if description and description != 'nan':
                            material_data['description'] = description
                    except:
                        pass
                
                if 'current_price' in column_mapping:
                    try:
                        material_data['current_price'] = float(row[column_mapping['current_price']])
                    except:
                        pass
                
                if 'minimum_quantity' in column_mapping:
                    try:
                        material_data['minimum_quantity'] = float(row[column_mapping['minimum_quantity']])
                    except:
                        pass
                
                if 'maximum_quantity' in column_mapping:
                    try:
                        material_data['maximum_quantity'] = float(row[column_mapping['maximum_quantity']])
                    except:
                        pass
                
                material = Material(**material_data)
                db.add(material)
                
                imported_materials.append({
                    'row': index + 1,
                    'name': name,
                    'code': code,
                    'unit': unit
                })
                
            except Exception as e:
                errors.append({
                    'row': index + 1,
                    'error': str(e),
                    'data': dict(row)
                })
        
        db.commit()
        
        return {
            "success": True,
            "message": f"Successfully imported {len(imported_materials)} materials",
            "imported_materials": len(imported_materials),
            "errors": errors,
            "summary": {
                "total_items": len(imported_materials),
                "errors_count": len(errors)
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing Excel file: {str(e)}")

@router.get("/export-materials")
def export_materials(
    *,
    db: Session = Depends(deps.get_db),
    workspace_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Export materials catalog to Excel file.
    """
    deps.get_workspace_access_sync(company_id, current_user, db)
    
    # Get materials
    materials = db.query(Material).filter(
        Material.workspace_id == company_id,
        Material.is_active == True
    ).all()
    
    export_data = []
    for material in materials:
        export_data.append({
            'Name': material.name,
            'Code': material.code,
            'Description': material.description or '',
            'Unit': material.unit,
            'Current Price': float(material.current_price) if material.current_price else 0,
            'Minimum Quantity': float(material.minimum_quantity) if material.minimum_quantity else 0,
            'Maximum Quantity': float(material.maximum_quantity) if material.maximum_quantity else 0,
            'Created Date': material.created_at.strftime('%Y-%m-%d') if material.created_at else ''
        })
    
    # Create Excel file
    df = pd.DataFrame(export_data)
    
    # Create Excel file in memory
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='Materials', index=False)
        
        # Get workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['Materials']
        
        # Add formatting
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#D7E4BC',
            'border': 1
        })
        
        # Format header row
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
        
        # Auto-adjust column widths
        for i, col in enumerate(df.columns):
            max_length = max(
                df[col].astype(str).map(len).max(),
                len(col)
            )
            worksheet.set_column(i, i, min(max_length + 2, 50))
    
    output.seek(0)
    
    # Create filename
    filename = f"Materials_Export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    return StreamingResponse(
        io.BytesIO(output.read()),
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

@router.get("/template-dpgf")
def download_dpgf_template() -> Any:
    """
    Download DPGF import template Excel file.
    """
    # Create template data
    template_data = {
        'Description': [
            'Excavation des terres',
            'Béton de fondation',
            'Maçonnerie mur porteur',
            'Charpente bois',
            'Couverture tuiles'
        ],
        'Unit': ['m³', 'm³', 'm²', 'm²', 'm²'],
        'Quantity': [25.5, 12.0, 150.0, 200.0, 180.0],
        'Unit Price': [45.00, 120.00, 85.00, 95.00, 65.00],
        'Total HT': [1147.50, 1440.00, 12750.00, 19000.00, 11700.00],
        'Purchase Budget': [1000.00, 1300.00, 12000.00, 18000.00, 11000.00],
        'Hours': [8.0, 6.0, 40.0, 50.0, 35.0],
        'Hourly Rate': [25.00, 25.00, 25.00, 25.00, 25.00],
        'CCTP Index': ['A.1.1', 'A.1.2', 'A.2.1', 'A.3.1', 'A.4.1'],
        'Sales Coefficient': [1.2, 1.2, 1.15, 1.15, 1.1]
    }
    
    df = pd.DataFrame(template_data)
    
    # Create Excel file in memory
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='DPGF Template', index=False)
        
        # Get workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['DPGF Template']
        
        # Add formatting
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#4472C4',
            'font_color': 'white',
            'border': 1
        })
        
        required_format = workbook.add_format({
            'bg_color': '#FFE6E6',
            'border': 1
        })
        
        optional_format = workbook.add_format({
            'bg_color': '#E6F3FF',
            'border': 1
        })
        
        # Format header row
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
        
        # Format required columns (first 4)
        for row in range(1, len(df) + 1):
            for col in range(4):
                worksheet.write(row, col, df.iloc[row-1, col], required_format)
        
        # Format optional columns
        for row in range(1, len(df) + 1):
            for col in range(4, len(df.columns)):
                worksheet.write(row, col, df.iloc[row-1, col], optional_format)
        
        # Auto-adjust column widths
        for i, col in enumerate(df.columns):
            max_length = max(
                df[col].astype(str).map(len).max(),
                len(col)
            )
            worksheet.set_column(i, i, min(max_length + 2, 25))
        
        # Add instructions
        worksheet.write(len(df) + 3, 0, "Instructions:", workbook.add_format({'bold': True, 'font_size': 14}))
        worksheet.write(len(df) + 4, 0, "• Required columns (red background): Description, Unit, Quantity, Unit Price")
        worksheet.write(len(df) + 5, 0, "• Optional columns (blue background): All others")
        worksheet.write(len(df) + 6, 0, "• Do not modify column headers")
        worksheet.write(len(df) + 7, 0, "• Use consistent units throughout")
        worksheet.write(len(df) + 8, 0, "• Total HT will be calculated automatically if not provided")
    
    output.seek(0)
    
    return StreamingResponse(
        io.BytesIO(output.read()),
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={"Content-Disposition": "attachment; filename=DPGF_Template.xlsx"}
    )

@router.get("/validate-excel")
async def validate_excel_file(
    *,
    file: UploadFile = File(...),
    import_type: str = 'dpgf'
) -> Any:
    """
    Validate Excel file structure before import.
    """
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="File must be Excel format (.xlsx or .xls)")
    
    try:
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))
        
        if import_type == 'dpgf':
            required_columns = ['description', 'unit', 'quantity', 'unit_price']
            optional_columns = ['total_ht', 'purchase_budget', 'hours', 'hourly_rate', 'cctp_index', 'sales_coefficient']
        elif import_type == 'materials':
            required_columns = ['name', 'code', 'unit']
            optional_columns = ['description', 'current_price', 'minimum_quantity', 'maximum_quantity']
        else:
            raise HTTPException(status_code=400, detail="Invalid import type")
        
        df_columns_lower = [col.lower().strip() for col in df.columns]
        
        # Check required columns
        found_required = []
        missing_required = []
        
        for req_col in required_columns:
            found = False
            for df_col in df_columns_lower:
                if req_col.lower() in df_col or df_col in req_col.lower():
                    found_required.append(req_col)
                    found = True
                    break
            if not found:
                missing_required.append(req_col)
        
        # Check optional columns
        found_optional = []
        for opt_col in optional_columns:
            for df_col in df_columns_lower:
                if opt_col.lower() in df_col or df_col in opt_col.lower():
                    found_optional.append(opt_col)
                    break
        
        # Analyze data quality
        data_issues = []
        if len(df) == 0:
            data_issues.append("File is empty")
        
        # Check for empty rows
        empty_rows = df.isnull().all(axis=1).sum()
        if empty_rows > 0:
            data_issues.append(f"{empty_rows} empty rows found")
        
        return {
            "valid": len(missing_required) == 0,
            "file_info": {
                "filename": file.filename,
                "rows": len(df),
                "columns": len(df.columns)
            },
            "columns": {
                "available": list(df.columns),
                "required_found": found_required,
                "required_missing": missing_required,
                "optional_found": found_optional
            },
            "data_quality": {
                "issues": data_issues,
                "empty_rows": empty_rows
            },
            "preview": df.head(3).to_dict('records') if len(df) > 0 else []
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error reading Excel file: {str(e)}")
