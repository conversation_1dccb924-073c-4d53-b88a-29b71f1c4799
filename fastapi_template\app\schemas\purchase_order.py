# app/schemas/purchase_order.py
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from decimal import Decimal
from app.models.purchase_order import PurchaseOrderStatus

class PurchaseOrderBase(BaseModel):
    order_number: Optional[str] = None
    order_date: Optional[datetime] = None
    expected_delivery_date: Optional[datetime] = None
    total_amount_ht: Optional[Decimal] = None
    vat_amount: Optional[Decimal] = None
    total_amount_ttc: Optional[Decimal] = None
    status: Optional[PurchaseOrderStatus] = PurchaseOrderStatus.DRAFT
    notes: Optional[str] = None

class PurchaseOrderCreate(PurchaseOrderBase):
    order_number: str
    order_date: datetime
    company_id: int
    supplier_id: int

class PurchaseOrderUpdate(PurchaseOrderBase):
    pass

class PurchaseOrderInDBBase(PurchaseOrderBase):
    id: Optional[int] = None
    company_id: Optional[int] = None
    project_id: Optional[int] = None
    supplier_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class PurchaseOrder(PurchaseOrderInDBBase):
    pass

class PurchaseOrderLine(BaseModel):
    id: Optional[int] = None
    purchase_order_id: int
    material_id: Optional[int] = None
    description: str
    quantity: Decimal
    unit_price: Decimal
    total_amount: Decimal

    class Config:
        from_attributes = True

class Delivery(BaseModel):
    id: Optional[int] = None
    purchase_order_id: int
    delivery_number: str
    delivery_date: datetime
    received_by: Optional[str] = None
    notes: Optional[str] = None

    class Config:
        from_attributes = True

class DeliveryLine(BaseModel):
    id: Optional[int] = None
    delivery_id: int
    purchase_order_line_id: int
    quantity_delivered: Decimal
    condition: Optional[str] = None
    notes: Optional[str] = None

    class Config:
        from_attributes = True