# app/models/user.py
from sqlalchemy import <PERSON><PERSON>an, Column, Integer, String, DateTime, Text, UUID
from sqlalchemy.dialects.postgresql import ENUM
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
import uuid
from app.core.database import Base

class UserRole(str, enum.Enum):
    SUPER_ADMIN = "SUPER_ADMIN"
    ADMIN = "ADMIN"
    CHEF_PROJET = "CHEF_PROJET"
    EMPLOYE = "EMPLOYE"
    CLIENT = "CLIENT"

# CompanyRole est maintenant défini dans app.models.company

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)  # Clé primaire locale auto-incrémentée
    supabase_user_id = Column(String, unique=True, index=True, nullable=True)  # UUID Supabase pour liaison auth
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=True)  # Optionnel car géré par Supabase
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)

    role = Column(ENUM(UserRole, name='userrole', create_constraint=False), default=UserRole.EMPLOYE)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    
    # Additional security fields
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime, nullable=True)
    
    # Profile fields
    phone = Column(String, nullable=True)
    avatar_url = Column(String, nullable=True)
    notes = Column(Text, nullable=True)

    # Relationships
    workspaces = relationship("UserWorkspace", back_populates="user", foreign_keys="UserWorkspace.user_id")
    time_entries = relationship("TimeEntry", back_populates="user")
    employee_assignments = relationship("EmployeeAssignment", back_populates="user")
    
    @property
    def full_name(self) -> str:
        """Return the full name computed from first and last name"""
        return f"{self.first_name} {self.last_name}" if self.first_name and self.last_name else ""
    
    @property
    def display_name(self) -> str:
        """Return the full name or email as display name"""
        return self.full_name or self.email
    
    def has_system_permission(self, permission: str) -> bool:
        """
        Vérifie les permissions système (pour super admin uniquement)
        """
        if self.role == UserRole.SUPER_ADMIN:
            return True  # Super admin a toutes les permissions système
        return False

    def has_workspace_permission(self, workspace_id: int, permission: str) -> bool:
        """
        Vérifie si l'utilisateur a une permission spécifique dans un espace de travail
        """
        # Trouver la relation UserWorkspace pour cet espace de travail
        for user_workspace in self.workspaces:
            if user_workspace.workspace_id == workspace_id and user_workspace.is_active:
                return user_workspace.has_permission(permission)
        return False

    def get_workspace_permissions(self, workspace_id: int) -> list:
        """
        Récupère toutes les permissions de l'utilisateur dans un espace de travail
        """
        for user_workspace in self.workspaces:
            if user_workspace.workspace_id == workspace_id and user_workspace.is_active:
                return user_workspace.get_permissions()
        return []

    def get_workspace_role(self, workspace_id: int) -> str:
        """
        Récupère le rôle de l'utilisateur dans un espace de travail
        """
        for user_workspace in self.workspaces:
            if user_workspace.workspace_id == workspace_id and user_workspace.is_active:
                return user_workspace.role_name
        return None

    # Méthodes de compatibilité (à supprimer plus tard)
    def has_company_permission(self, company_id: int, permission: str) -> bool:
        return self.has_workspace_permission(company_id, permission)

    def get_company_permissions(self, company_id: int) -> list:
        return self.get_workspace_permissions(company_id)

    def get_company_role(self, company_id: int) -> str:
        return self.get_workspace_role(company_id)

    def can_access_project(self, project_id: int, workspace_id: int) -> bool:
        """
        Vérifie si l'utilisateur peut accéder à un projet spécifique
        """
        if self.role == UserRole.SUPER_ADMIN:
            return True

        # Vérifier les permissions dans l'espace de travail
        return self.has_workspace_permission(workspace_id, "projects.read")
    
    def is_account_locked(self) -> bool:
        """Check if account is temporarily locked"""
        if self.locked_until:
            return datetime.utcnow() < self.locked_until
        return False