#!/usr/bin/env python3
"""
Script pour corriger toutes les références aux anciens noms après la migration companies -> workspaces
"""

import os
import re
import sys
from pathlib import Path

def fix_file_content(file_path: str, content: str) -> str:
    """Applique toutes les corrections nécessaires au contenu d'un fichier"""
    
    # Skip migration files and compatibility files
    if any(skip in file_path for skip in [
        'alembic/versions/', 
        'run_migration.py',
        'deps_old.py',
        '__init__.py',  # Skip model/schema __init__.py files with compatibility imports
        'companies.py',  # Skip old companies endpoint
        'admin_companies.py'  # Skip old admin companies endpoint
    ]):
        print(f"   ⏭️  Skipping {file_path} (migration/compatibility file)")
        return content
    
    original_content = content
    
    # 1. Import corrections
    content = re.sub(
        r'from app\.models\.workspace import.*UserCompany.*',
        'from app.models.workspace import UserWorkspace, Workspace',
        content
    )
    
    content = re.sub(
        r'from app\.models\.workspace import.*Company.*UserCompany.*',
        'from app.models.workspace import UserWorkspace, Workspace',
        content
    )
    
    # 2. Model class name corrections
    content = re.sub(r'\bUserCompany\b', 'UserWorkspace', content)
    content = re.sub(r'\bCompany\b(?!\w)', 'Workspace', content)  # Avoid replacing "CompanyRole" etc.
    
    # 3. Attribute corrections
    content = re.sub(r'\.company_id\b', '.workspace_id', content)
    content = re.sub(r'company_id\s*=', 'workspace_id =', content)
    content = re.sub(r'company_id\s*:', 'workspace_id:', content)
    
    # 4. Variable name corrections
    content = re.sub(r'\buser_company\b', 'user_workspace', content)
    content = re.sub(r'\buser_company_id\b', 'user_workspace_id', content)
    
    # 5. Comment and string corrections
    content = re.sub(r'entreprise de l\'utilisateur', 'espace de travail de l\'utilisateur', content)
    content = re.sub(r'associé à une entreprise', 'associé à un espace de travail', content)
    content = re.sub(r'UserCompany', 'UserWorkspace', content)
    content = re.sub(r'via UserCompany', 'via UserWorkspace', content)
    
    # 6. Specific logic corrections for projects
    if 'projects_crud.py' in file_path:
        # Fix project filtering logic to use workspace -> entreprises_tiers
        content = re.sub(
            r'ProjectCompany\.workspace_id == user_workspace\.workspace_id',
            'ProjectCompany.company_id.in_(workspace_companies_subquery)',
            content
        )
    
    if content != original_content:
        print(f"   ✅ Fixed {file_path}")
        return content
    else:
        print(f"   ⏭️  No changes needed in {file_path}")
        return content

def fix_all_files():
    """Parcourt tous les fichiers Python et applique les corrections"""
    
    print("🔄 Starting global company -> workspace reference fixes...")
    
    # Directories to process
    directories = [
        'app/api/api_v1/endpoints/',
        'app/middleware/',
        'app/schemas/',
        'app/models/'
    ]
    
    total_files = 0
    fixed_files = 0
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            print(f"   ⚠️  Directory {directory} not found, skipping...")
            continue
            
        print(f"\n📁 Processing {directory}...")
        
        for file_path in dir_path.rglob("*.py"):
            total_files += 1
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    original_content = f.read()
                
                fixed_content = fix_file_content(str(file_path), original_content)
                
                if fixed_content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(fixed_content)
                    fixed_files += 1
                    
            except Exception as e:
                print(f"   ❌ Error processing {file_path}: {e}")
    
    print(f"\n🎉 Global fixes completed!")
    print(f"📊 Processed {total_files} files, fixed {fixed_files} files")

if __name__ == "__main__":
    # Change to the fastapi_template directory
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    fix_all_files()
