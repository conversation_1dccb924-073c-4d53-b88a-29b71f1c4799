# app/api/deps.py
"""
Dépendances FastAPI pour l'authentification et l'autorisation
Version propre et simplifiée
"""

from typing import Optional
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db
from app.models.user import User
from app.models.workspace import Workspace, UserWorkspace


# Fonctions d'accès aux workspaces
async def get_workspace_access(
    workspace_id: int,
    current_user: User,
    db: AsyncSession
) -> Workspace:
    """Vérifier que l'utilisateur a accès à l'espace de travail spécifié"""
    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == current_user.id,
            UserWorkspace.workspace_id == workspace_id,
            UserWorkspace.is_active == True
        )
    )
    user_workspace = result.scalar_one_or_none()
    
    if not user_workspace:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Accès refusé à cet espace de travail"
        )
    
    result = await db.execute(select(Workspace).where(Workspace.id == workspace_id))
    workspace = result.scalar_one_or_none()
    if not workspace or not workspace.is_active:
        raise HTTPException(status_code=404, detail="Espace de travail non trouvé")
    
    return workspace


async def require_workspace_admin(
    workspace_id: int,
    current_user: User,
    db: AsyncSession
) -> UserWorkspace:
    """Exiger que l'utilisateur soit admin de l'espace de travail spécifié"""
    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == current_user.id,
            UserWorkspace.workspace_id == workspace_id,
            UserWorkspace.role_name.in_(["ADMIN", "MANAGER"]),
            UserWorkspace.is_active == True
        )
    )
    user_workspace = result.scalar_one_or_none()
    
    if not user_workspace:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Droits d'administration requis pour cet espace de travail"
        )
    
    return user_workspace


async def get_user_workspaces(
    current_user: User,
    db: AsyncSession
) -> list[UserWorkspace]:
    """Récupérer tous les espaces de travail de l'utilisateur"""
    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == current_user.id,
            UserWorkspace.is_active == True
        )
    )
    return result.scalars().all()


async def get_user_default_workspace(
    current_user: User,
    db: AsyncSession
) -> Optional[UserWorkspace]:
    """Récupérer l'espace de travail par défaut de l'utilisateur"""
    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == current_user.id,
            UserWorkspace.is_default == True,
            UserWorkspace.is_active == True
        )
    )
    return result.scalar_one_or_none()


# Alias de compatibilité pour les anciens noms
async def get_company_access(
    company_id: int,
    current_user: User,
    db: AsyncSession
) -> Workspace:
    """Alias de compatibilité pour get_workspace_access"""
    return await get_workspace_access(company_id, current_user, db)


async def require_company_admin(
    company_id: int,
    current_user: User,
    db: AsyncSession
) -> UserWorkspace:
    """Alias de compatibilité pour require_workspace_admin"""
    return await require_workspace_admin(company_id, current_user, db)


async def get_user_companies(
    current_user: User,
    db: AsyncSession
) -> list[UserWorkspace]:
    """Alias de compatibilité pour get_user_workspaces"""
    return await get_user_workspaces(current_user, db)


async def get_user_default_company(
    current_user: User,
    db: AsyncSession
) -> Optional[UserWorkspace]:
    """Alias de compatibilité pour get_user_default_workspace"""
    return await get_user_default_workspace(current_user, db)
