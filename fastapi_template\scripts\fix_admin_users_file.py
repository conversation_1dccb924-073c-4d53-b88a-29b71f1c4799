#!/usr/bin/env python3
"""
Script pour corriger spécifiquement le fichier admin_users.py
"""

import re

def fix_admin_users():
    """Corrige le fichier admin_users.py"""
    
    file_path = "app/api/api_v1/endpoints/admin_users.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"🔄 Correction de {file_path}...")
        
        # Remplacer tous les company_id par workspace_id dans les paramètres de fonction
        content = re.sub(r'company_id: int', 'workspace_id: int', content)
        
        # Remplacer toutes les utilisations de company_id par workspace_id
        content = re.sub(r'\bcompany_id\b', 'workspace_id', content)
        
        # Corriger les noms de schémas
        content = re.sub(r'UserWithCompanies', 'UserWithWorkspaces', content)
        content = re.sub(r'CompanyRoleAssignment', 'WorkspaceRoleAssignment', content)
        content = re.sub(r'CompanyWithUsers', 'WorkspaceWithUsers', content)
        
        # Corriger les commentaires et messages
        content = re.sub(r'entreprise', 'espace de travail', content)
        content = re.sub(r'Entreprise', 'Espace de travail', content)
        content = re.sub(r"l'entreprise", "l'espace de travail", content)
        content = re.sub(r"dans l'entreprise", "dans l'espace de travail", content)
        
        # Corriger les paths d'API
        content = re.sub(r'/workspaces/{company_id}', '/workspaces/{workspace_id}', content)
        content = re.sub(r'/workspaces/{company_id:int}', '/workspaces/{workspace_id:int}', content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ {file_path} corrigé avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur lors de la correction de {file_path}: {e}")

if __name__ == "__main__":
    fix_admin_users()
