# app/schemas/__init__.py
from app.schemas.user import User, UserCreate, UserUpdate, UserInDB, UserWithWorkspaces, UserPermissionSummary
from app.schemas.workspace import (
    Workspace, WorkspaceCreate, WorkspaceUpdate, UserWorkspace, WorkspaceSettings,
    WorkspaceRoleAssignment, WorkspaceWithUsers, WorkspaceComplete, WorkspaceInvitation,
    WorkspaceRolePermission
)
from app.schemas.rbac import (
    Role, RoleCreate, RoleUpdate, Permission, PermissionCreate, PermissionUpdate,
    RolePermissionsConfig, UserPermissions, PermissionCheck
)
from app.schemas.project import Project, ProjectCreate, ProjectUpdate, ProjectDocument, ProjectEmployee
from app.schemas.employee import Employee, EmployeeCreate, EmployeeUpdate, TimeEntry, TimeEntryCreate, EmployeeAssignment
from app.schemas.supplier import Supplier, SupplierCreate, SupplierUpdate, SupplierContact
from app.schemas.material import Material, MaterialCreate, MaterialUpdate, MaterialCategory, TechnicalSheet, PriceHistory
from app.schemas.financial import Budget, BudgetCreate, BudgetUpdate, BudgetLine, Invoice, InvoiceCreate, Payment, FinancialReport
from app.schemas.purchase_order import PurchaseOrder, PurchaseOrderCreate, PurchaseOrderUpdate, PurchaseOrderLine, Delivery, DeliveryLine
from app.schemas.quote import Quote, QuoteCreate, QuoteUpdate, QuoteLine, QuoteTemplate
from app.schemas.document import Document, DocumentCreate, DocumentUpdate, DocumentVersion, DocumentFolder
from app.schemas.technical_document import (
    TechnicalDocumentResponse, TechnicalDocumentCreate, TechnicalDocumentUpdate,
    TechnicalDocumentList, TechnicalDocumentCompanyCreate, TechnicalDocumentCompanyResponse,
    TextEnhancementRequest, TextEnhancementResponse, TechnicalDocumentFilter,
    TechnicalDocumentSearchResponse, TechnicalDocumentStats, DocumentType
)

__all__ = [
    # User schemas
    "User", "UserCreate", "UserUpdate", "UserInDB", "UserWithWorkspaces", "UserPermissionSummary",

    # Workspace schemas (nouveaux)
    "Workspace", "WorkspaceCreate", "WorkspaceUpdate", "UserWorkspace", "WorkspaceSettings",
    "WorkspaceRoleAssignment", "WorkspaceWithUsers", "WorkspaceComplete", "WorkspaceInvitation",
    "WorkspaceRolePermission",

    # Company schemas (compatibilité)
    "Company", "CompanyCreate", "CompanyUpdate", "UserCompany", "CompanySettings",
    "CompanyRoleAssignment", "CompanyWithUsers", "CompanyInvitation", "CompanyRolePermission",

    # RBAC schemas
    "Role", "RoleCreate", "RoleUpdate", "Permission", "PermissionCreate", "PermissionUpdate",
    "RolePermissionsConfig", "UserPermissions", "PermissionCheck",

    # Other schemas
    "Project", "ProjectCreate", "ProjectUpdate", "ProjectDocument", "ProjectEmployee",
    "Employee", "EmployeeCreate", "EmployeeUpdate", "TimeEntry", "TimeEntryCreate", "EmployeeAssignment",
    "Supplier", "SupplierCreate", "SupplierUpdate", "SupplierContact",
    "Material", "MaterialCreate", "MaterialUpdate", "MaterialCategory", "TechnicalSheet", "PriceHistory",
    "Budget", "BudgetCreate", "BudgetUpdate", "BudgetLine", "Invoice", "InvoiceCreate", "Payment", "FinancialReport",
    "PurchaseOrder", "PurchaseOrderCreate", "PurchaseOrderUpdate", "PurchaseOrderLine", "Delivery", "DeliveryLine",
    "Quote", "QuoteCreate", "QuoteUpdate", "QuoteLine", "QuoteTemplate",
    "Document", "DocumentCreate", "DocumentUpdate", "DocumentVersion", "DocumentFolder",

    # Technical Document schemas
    "TechnicalDocumentResponse", "TechnicalDocumentCreate", "TechnicalDocumentUpdate",
    "TechnicalDocumentList", "TechnicalDocumentCompanyCreate", "TechnicalDocumentCompanyResponse",
    "TextEnhancementRequest", "TextEnhancementResponse", "TechnicalDocumentFilter",
    "TechnicalDocumentSearchResponse", "TechnicalDocumentStats", "DocumentType"
]