# app/api/api_v1/endpoints/rbac.py
"""
Endpoints pour la gestion du système RBAC (Role-Based Access Control)
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.middleware.auth_sync_middleware import require_auth
from app.services.rbac_service import RBACService
from app.schemas.rbac import (
    Role, Permission, RolePermissionsConfig, UserPermissions, 
    PermissionCheck, BulkPermissionUpdate, PermissionMatrix
)
from app.schemas.workspace import CompanyRoleAssignment

router = APIRouter()

# Dependency pour vérifier les droits super admin
async def require_super_admin(request: Request) -> Dict[str, Any]:
    """Dependency pour exiger un rôle super admin"""
    user = await require_auth(request)
    user_role = user.get("role", "").upper()
    is_superuser = user.get("is_superuser", False)
    
    if user_role != "SUPER_ADMIN" and not is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Accès refusé. Droits super administrateur requis."
        )
    
    return user

@router.get("/roles", response_model=List[str])
async def get_available_roles(
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """Récupère la liste de tous les rôles disponibles"""
    rbac_service = RBACService(db)
    return await rbac_service.get_available_roles()

@router.get("/permissions", response_model=List[Dict[str, str]])
async def get_all_permissions(
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """Récupère toutes les permissions disponibles"""
    rbac_service = RBACService(db)
    return await rbac_service.get_all_permissions()

@router.get("/companies/{company_id}/permissions-matrix")
async def get_company_permission_matrix(
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """Récupère la matrice des permissions pour une entreprise"""
    rbac_service = RBACService(db)
    return await rbac_service.get_company_permission_matrix(company_id)

@router.get("/companies/{company_id}/roles/{role_name}/permissions", response_model=List[str])
async def get_role_permissions(
    company_id: int,
    role_name: str,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """Récupère les permissions d'un rôle dans une entreprise"""
    rbac_service = RBACService(db)
    return await rbac_service.get_role_permissions(company_id, role_name)

@router.post("/companies/{company_id}/roles/{role_name}/permissions")
async def set_role_permissions(
    company_id: int,
    role_name: str,
    permissions: List[str],
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """Définit les permissions d'un rôle dans une entreprise"""
    rbac_service = RBACService(db)
    success = await rbac_service.set_role_permissions(company_id, role_name, permissions)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la mise à jour des permissions"
        )
    
    return {"message": f"Permissions mises à jour pour le rôle {role_name}"}

@router.post("/companies/{company_id}/roles/{role_name}/permissions/add")
async def add_permissions_to_role(
    company_id: int,
    role_name: str,
    permissions: List[str],
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """Ajoute des permissions à un rôle dans une entreprise"""
    rbac_service = RBACService(db)
    success = await rbac_service.add_permissions_to_role(company_id, role_name, permissions)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de l'ajout des permissions"
        )
    
    return {"message": f"Permissions ajoutées au rôle {role_name}"}

@router.post("/companies/{company_id}/roles/{role_name}/permissions/remove")
async def remove_permissions_from_role(
    company_id: int,
    role_name: str,
    permissions: List[str],
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """Supprime des permissions d'un rôle dans une entreprise"""
    rbac_service = RBACService(db)
    success = await rbac_service.remove_permissions_from_role(company_id, role_name, permissions)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la suppression des permissions"
        )
    
    return {"message": f"Permissions supprimées du rôle {role_name}"}

@router.get("/users/{user_id}/companies/{company_id}/permissions", response_model=List[str])
async def get_user_permissions(
    user_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """Récupère les permissions d'un utilisateur dans une entreprise"""
    rbac_service = RBACService(db)
    return await rbac_service.get_user_permissions(user_id, company_id)

@router.post("/users/{user_id}/companies/{company_id}/check-permission")
async def check_user_permission(
    user_id: int,
    company_id: int,
    permission: str,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """Vérifie si un utilisateur a une permission spécifique"""
    rbac_service = RBACService(db)
    has_permission = await rbac_service.check_permission(user_id, company_id, permission)
    
    return {
        "user_id": user_id,
        "company_id": company_id,
        "permission": permission,
        "has_permission": has_permission
    }

@router.post("/users/{user_id}/companies/{company_id}/assign-role")
async def assign_user_role(
    user_id: int,
    company_id: int,
    role_assignment: CompanyRoleAssignment,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """Assigne un rôle à un utilisateur dans une entreprise"""
    rbac_service = RBACService(db)
    success = await rbac_service.assign_user_role(
        role_assignment.user_id, 
        role_assignment.company_id, 
        role_assignment.role_name
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Utilisateur ou entreprise non trouvé"
        )
    
    return {"message": f"Rôle {role_assignment.role_name} assigné à l'utilisateur"}

@router.post("/companies/{company_id}/bulk-permissions")
async def bulk_update_permissions(
    company_id: int,
    update_data: BulkPermissionUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """Mise à jour en lot des permissions d'un rôle"""
    rbac_service = RBACService(db)
    
    try:
        # Ajouter les permissions
        if update_data.add_permissions:
            await rbac_service.add_permissions_to_role(
                company_id, 
                update_data.role_name, 
                update_data.add_permissions
            )
        
        # Supprimer les permissions
        if update_data.remove_permissions:
            await rbac_service.remove_permissions_from_role(
                company_id, 
                update_data.role_name, 
                update_data.remove_permissions
            )
        
        return {"message": "Permissions mises à jour avec succès"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la mise à jour: {str(e)}"
        )
