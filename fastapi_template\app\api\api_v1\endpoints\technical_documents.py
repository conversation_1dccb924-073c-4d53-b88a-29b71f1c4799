# app/api/api_v1/endpoints/technical_documents.py
"""
API endpoints pour les documents techniques (CCTP/DPGF)
"""

from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, delete
from sqlalchemy.orm import selectinload

from app.api import deps
from app.models.user import User
from app.models.workspace import UserWorkspace, Workspace
from app.models.project import Project
from app.models.document import TechnicalDocument, TechnicalDocumentCompany, DocumentType
from app.models.entreprise_tiers import EntrepriseTiers
from app.schemas.technical_document import (
    TechnicalDocumentCreate,
    TechnicalDocumentUpdate,
    TechnicalDocumentResponse,
    TechnicalDocumentList,
    TechnicalDocumentCompanyCreate,
    TechnicalDocumentCompanyResponse,
    TechnicalDocumentFilter,
    TechnicalDocumentSearchResponse,
    TechnicalDocumentStats,
    TextEnhancementRequest,
    TextEnhancementResponse
)
from app.core.jwt_auth import require_auth
from app.services.chatgpt_service import chatgpt_service

router = APIRouter()

async def get_user_workspace_id(current_user: Dict[str, Any], db: AsyncSession) -> int:
    """Récupère l'ID de l'espace de travail de l'utilisateur connecté"""
    user_id = current_user.get("user_id")
    if not user_id:
        raise HTTPException(status_code=400, detail="Token utilisateur invalide")

    # Récupérer l'espace de travail de l'utilisateur
    user_workspace_result = await db.execute(
        select(UserWorkspace).where(
            and_(
                UserWorkspace.user_id == user_id,
                UserWorkspace.is_active == True
            )
        ).limit(1)
    )
    user_workspace = user_workspace_result.scalar_one_or_none()
    if not user_workspace:
        raise HTTPException(status_code=403, detail="Utilisateur non associé à un espace de travail")

    return user_workspace.workspace_id

async def check_document_access(document_id: int, user_workspace_id: int, db: AsyncSession) -> TechnicalDocument:
    """Vérifie l'accès à un document technique via le workspace de l'utilisateur"""
    # Récupérer le document avec ses relations
    result = await db.execute(
        select(TechnicalDocument)
        .options(
            selectinload(TechnicalDocument.companies),
            selectinload(TechnicalDocument.project),
            selectinload(TechnicalDocument.creator),
            selectinload(TechnicalDocument.updater)
        )
        .where(TechnicalDocument.id == document_id)
    )
    document = result.scalar_one_or_none()

    if not document:
        raise HTTPException(status_code=404, detail="Document non trouvé")

    # Vérifier l'accès via les entreprises tierces du workspace
    # Récupérer les IDs des entreprises tierces du workspace
    workspace_companies_result = await db.execute(
        select(EntrepriseTiers.id).where(EntrepriseTiers.workspace_id == user_workspace_id)
    )
    workspace_company_ids = {row[0] for row in workspace_companies_result.fetchall()}

    # Vérifier si le document est associé à au moins une entreprise tierce du workspace
    has_access = any(
        company_rel.workspace_id in workspace_company_ids
        for company_rel in document.companies
    )

    if not has_access:
        raise HTTPException(status_code=403, detail="Accès refusé à ce document")

    return document

@router.get("/", response_model=List[TechnicalDocumentList])
async def get_technical_documents(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
    project_id: Optional[int] = Query(None, description="Filtrer par projet"),
    type_document: Optional[DocumentType] = Query(None, description="Filtrer par type de document"),
    skip: int = Query(0, ge=0, description="Nombre d'éléments à ignorer"),
    limit: int = Query(100, ge=1, le=1000, description="Nombre d'éléments à retourner"),
) -> Any:
    """
    Récupérer la liste des documents techniques accessibles à l'utilisateur
    """
    user_workspace_id = await get_user_workspace_id(current_user, db)

    # Construire la requête de base
    query = select(TechnicalDocument).options(
        selectinload(TechnicalDocument.project),
        selectinload(TechnicalDocument.creator),
        selectinload(TechnicalDocument.companies)
    ).where(TechnicalDocument.is_active == True)

    # Filtrer par accès aux entreprises tierces du workspace de l'utilisateur
    query = query.join(TechnicalDocumentCompany).join(EntrepriseTiers).where(
        EntrepriseTiers.workspace_id == user_workspace_id
    )
    
    # Appliquer les filtres
    if project_id:
        query = query.where(TechnicalDocument.project_id == project_id)
    
    if type_document:
        query = query.where(TechnicalDocument.type_document == type_document)
    
    # Pagination
    query = query.offset(skip).limit(limit)
    
    result = await db.execute(query)
    documents = result.scalars().all()
    
    # Transformer en réponse avec comptage des companies
    response_documents = []
    for doc in documents:
        doc_dict = {
            "id": doc.id,
            "name": doc.name,
            "type_document": doc.type_document,
            "content": None,  # Pas de contenu dans la liste
            "project_id": doc.project_id,
            "created_by": doc.created_by,
            "updated_by": doc.updated_by,
            "is_active": doc.is_active,
            "created_at": doc.created_at,
            "updated_at": doc.updated_at,
            "project": {
                "id": doc.project.id,
                "name": doc.project.name,
                "code": doc.project.code
            } if doc.project else None,
            "creator": {
                "id": doc.creator.id,
                "email": doc.creator.email,
                "first_name": doc.creator.first_name,
                "last_name": doc.creator.last_name
            } if doc.creator else None,
            "company_count": len(doc.companies)
        }
        response_documents.append(doc_dict)
    
    return response_documents

@router.get("/{document_id}", response_model=TechnicalDocumentResponse)
async def get_technical_document(
    *,
    db: AsyncSession = Depends(deps.get_db),
    document_id: int,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Récupérer un document technique par son ID
    """
    user_workspace_id = await get_user_workspace_id(current_user, db)
    document = await check_document_access(document_id, user_workspace_id, db)
    
    # Récupérer les entreprises tierces associées
    companies_result = await db.execute(
        select(EntrepriseTiers)
        .join(TechnicalDocumentCompany)
        .where(TechnicalDocumentCompany.technical_document_id == document_id)
    )
    companies = companies_result.scalars().all()
    
    return {
        "id": document.id,
        "name": document.name,
        "type_document": document.type_document,
        "content": document.content,
        "project_id": document.project_id,
        "created_by": document.created_by,
        "updated_by": document.updated_by,
        "is_active": document.is_active,
        "created_at": document.created_at,
        "updated_at": document.updated_at,
        "project": {
            "id": document.project.id,
            "name": document.project.name,
            "code": document.project.code
        } if document.project else None,
        "creator": {
            "id": document.creator.id,
            "email": document.creator.email,
            "first_name": document.creator.first_name,
            "last_name": document.creator.last_name
        } if document.creator else None,
        "updater": {
            "id": document.updater.id,
            "email": document.updater.email,
            "first_name": document.updater.first_name,
            "last_name": document.updater.last_name
        } if document.updater else None,
        "companies": [
            {
                "id": company.id,
                "name": company.name,
                "code": company.code
            }
            for company in companies
        ]
    }

@router.post("/", response_model=TechnicalDocumentResponse)
async def create_technical_document(
    *,
    db: AsyncSession = Depends(deps.get_db),
    document_in: TechnicalDocumentCreate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Créer un nouveau document technique
    """
    user_id = current_user.get("user_id")
    user_workspace_id = await get_user_workspace_id(current_user, db)
    
    # Vérifier que le projet existe et est accessible
    project_result = await db.execute(
        select(Project).where(Project.id == document_in.project_id)
    )
    project = project_result.scalar_one_or_none()
    if not project:
        raise HTTPException(status_code=404, detail="Projet non trouvé")
    
    # Créer le document
    document_data = document_in.model_dump(exclude={"company_ids"})
    document_data["created_by"] = user_id
    
    document = TechnicalDocument(**document_data)
    db.add(document)
    await db.flush()  # Pour obtenir l'ID
    
    # Associer les entreprises tierces (valider qu'elles appartiennent au workspace)
    company_ids = set(document_in.company_ids or [])

    for company_id in company_ids:
        # Vérifier que l'entreprise tierce existe et appartient au workspace
        company_result = await db.execute(
            select(EntrepriseTiers).where(
                and_(
                    EntrepriseTiers.id == company_id,
                    EntrepriseTiers.workspace_id == user_workspace_id
                )
            )
        )
        if company_result.scalar_one_or_none():
            doc_company = TechnicalDocumentCompany(
                technical_document_id=document.id,
                workspace_id =company_id
            )
            db.add(doc_company)
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Entreprise tierce {company_id} non trouvée ou non accessible"
            )
    
    await db.commit()
    await db.refresh(document)
    
    # Retourner le document créé
    return await get_technical_document(
        db=db,
        document_id=document.id,
        current_user=current_user
    )

@router.put("/{document_id}", response_model=TechnicalDocumentResponse)
async def update_technical_document(
    *,
    db: AsyncSession = Depends(deps.get_db),
    document_id: int,
    document_in: TechnicalDocumentUpdate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Mettre à jour un document technique
    """
    user_id = current_user.get("user_id")
    user_workspace_id = await get_user_workspace_id(current_user, db)
    document = await check_document_access(document_id, user_workspace_id, db)

    # Mettre à jour les champs du document
    update_data = document_in.model_dump(exclude_unset=True, exclude={"company_ids"})
    if update_data:
        update_data["updated_by"] = user_id
        for field, value in update_data.items():
            setattr(document, field, value)

    # Gérer les relations companies si spécifiées
    if document_in.company_ids is not None:
        # Supprimer les relations existantes
        await db.execute(
            delete(TechnicalDocumentCompany).where(
                TechnicalDocumentCompany.technical_document_id == document_id
            )
        )

        # Ajouter les nouvelles relations (valider qu'elles appartiennent au workspace)
        company_ids = set(document_in.company_ids)

        for company_id in company_ids:
            # Vérifier que l'entreprise tierce existe et appartient au workspace
            company_result = await db.execute(
                select(EntrepriseTiers).where(
                    and_(
                        EntrepriseTiers.id == company_id,
                        EntrepriseTiers.workspace_id == user_workspace_id
                    )
                )
            )
            if company_result.scalar_one_or_none():
                doc_company = TechnicalDocumentCompany(
                    technical_document_id=document_id,
                    workspace_id =company_id
                )
                db.add(doc_company)
            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"Entreprise tierce {company_id} non trouvée ou non accessible"
                )

    await db.commit()
    await db.refresh(document)

    return await get_technical_document(
        db=db,
        document_id=document_id,
        current_user=current_user
    )

@router.delete("/{document_id}")
async def delete_technical_document(
    *,
    db: AsyncSession = Depends(deps.get_db),
    document_id: int,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Supprimer un document technique (soft delete)
    """
    user_workspace_id = await get_user_workspace_id(current_user, db)
    document = await check_document_access(document_id, user_workspace_id, db)

    # Soft delete
    document.is_active = False
    document.updated_by = current_user.get("user_id")

    await db.commit()

    return {"message": "Document supprimé avec succès"}

# Endpoints pour la gestion des relations companies

@router.post("/{document_id}/companies", response_model=List[TechnicalDocumentCompanyResponse])
async def add_companies_to_document(
    *,
    db: AsyncSession = Depends(deps.get_db),
    document_id: int,
    company_ids: List[int],
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Associer des companies à un document technique
    """
    user_workspace_id = await get_user_workspace_id(current_user, db)
    document = await check_document_access(document_id, user_workspace_id, db)

    added_relations = []

    for company_id in company_ids:
        # Vérifier que l'entreprise tierce existe et appartient au workspace
        company_result = await db.execute(
            select(EntrepriseTiers).where(
                and_(
                    EntrepriseTiers.id == company_id,
                    EntrepriseTiers.workspace_id == user_workspace_id
                )
            )
        )
        if not company_result.scalar_one_or_none():
            raise HTTPException(
                status_code=400,
                detail=f"Entreprise tierce {company_id} non trouvée ou non accessible"
            )

        # Vérifier si la relation existe déjà
        existing_result = await db.execute(
            select(TechnicalDocumentCompany).where(
                and_(
                    TechnicalDocumentCompany.technical_document_id == document_id,
                    TechnicalDocumentCompany.workspace_id == company_id
                )
            )
        )
        if existing_result.scalar_one_or_none():
            continue

        # Créer la nouvelle relation
        doc_company = TechnicalDocumentCompany(
            technical_document_id=document_id,
            workspace_id =company_id
        )
        db.add(doc_company)
        await db.flush()
        added_relations.append(doc_company)

    await db.commit()

    return [
        {
            "id": rel.id,
            "technical_document_id": rel.technical_document_id,
            "workspace_id": rel.workspace_id,
            "created_at": rel.created_at
        }
        for rel in added_relations
    ]

@router.delete("/{document_id}/companies/{company_id}")
async def remove_company_from_document(
    *,
    db: AsyncSession = Depends(deps.get_db),
    document_id: int,
    workspace_id: int,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Supprimer l'association entre un document et une company
    """
    user_workspace_id = await get_user_workspace_id(current_user, db)
    document = await check_document_access(document_id, user_workspace_id, db)

    # Vérifier que l'entreprise tierce appartient au workspace
    company_result = await db.execute(
        select(EntrepriseTiers).where(
            and_(
                EntrepriseTiers.id == company_id,
                EntrepriseTiers.workspace_id == user_workspace_id
            )
        )
    )
    if not company_result.scalar_one_or_none():
        raise HTTPException(
            status_code=400,
            detail="Entreprise tierce non trouvée ou non accessible"
        )

    # Supprimer la relation
    result = await db.execute(
        delete(TechnicalDocumentCompany).where(
            and_(
                TechnicalDocumentCompany.technical_document_id == document_id,
                TechnicalDocumentCompany.workspace_id == company_id
            )
        )
    )

    if result.rowcount == 0:
        raise HTTPException(status_code=404, detail="Association non trouvée")

    await db.commit()

    return {"message": "Association supprimée avec succès"}

# Endpoints pour l'amélioration de texte avec ChatGPT

@router.post("/enhance-text", response_model=TextEnhancementResponse)
async def enhance_text(
    *,
    enhancement_request: TextEnhancementRequest,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Améliorer un texte avec ChatGPT
    """
    # Vérifier que le service ChatGPT est disponible
    if not chatgpt_service.is_available():
        raise HTTPException(
            status_code=503,
            detail="Service d'amélioration de texte non disponible. Veuillez configurer OPENAI_API_KEY."
        )

    try:
        result = await chatgpt_service.enhance_text(
            text=enhancement_request.text,
            prompt_type=enhancement_request.prompt_type,
            document_type=enhancement_request.document_type,
            context=enhancement_request.context
        )

        if not result.get("success", False):
            raise HTTPException(
                status_code=500,
                detail=f"Erreur lors de l'amélioration du texte: {result.get('error', 'Erreur inconnue')}"
            )

        return {
            "original_text": result["original_text"],
            "enhanced_text": result["enhanced_text"],
            "prompt_type": result["prompt_type"],
            "document_type": result["document_type"],
            "processing_time": result.get("processing_time")
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de l'amélioration du texte: {str(e)}"
        )

@router.get("/prompt-types/{document_type}")
async def get_available_prompt_types(
    *,
    document_type: DocumentType,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Récupérer les types de prompts disponibles pour un type de document
    """
    try:
        prompt_types = chatgpt_service.get_available_prompt_types(document_type)
        return {
            "document_type": document_type,
            "available_prompt_types": prompt_types
        }
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Erreur: {str(e)}"
        )

@router.get("/supported-document-types")
async def get_supported_document_types(
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Récupérer les types de documents supportés pour l'amélioration de texte
    """
    return {
        "supported_document_types": chatgpt_service.get_supported_document_types()
    }
