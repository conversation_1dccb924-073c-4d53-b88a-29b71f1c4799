#!/usr/bin/env python3
"""
Script pour ajouter les colonnes logo_url et logo_filename à la table entreprises_tiers
"""

import asyncio
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.database import engine

async def add_logo_columns():
    """Ajoute les colonnes logo_url et logo_filename à la table entreprises_tiers"""
    
    try:
        async with engine.begin() as conn:
            print("🔄 Ajout des colonnes logo à la table entreprises_tiers...")
            
            # Vérifier si les colonnes existent déjà
            check_columns_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'entreprises_tiers' 
                AND column_name IN ('logo_url', 'logo_filename')
            """)
            
            result = await conn.execute(check_columns_query)
            existing_columns = [row[0] for row in result.fetchall()]
            
            # Ajouter logo_url si elle n'existe pas
            if 'logo_url' not in existing_columns:
                await conn.execute(text("""
                    ALTER TABLE entreprises_tiers 
                    ADD COLUMN logo_url VARCHAR(500)
                """))
                print("✅ Colonne logo_url ajoutée")
            else:
                print("ℹ️  Colonne logo_url existe déjà")
            
            # Ajouter logo_filename si elle n'existe pas
            if 'logo_filename' not in existing_columns:
                await conn.execute(text("""
                    ALTER TABLE entreprises_tiers 
                    ADD COLUMN logo_filename VARCHAR(255)
                """))
                print("✅ Colonne logo_filename ajoutée")
            else:
                print("ℹ️  Colonne logo_filename existe déjà")
            
            print("🎉 Migration terminée avec succès!")
            
    except Exception as e:
        print(f"❌ Erreur lors de la migration: {e}")
        raise
    finally:
        await engine.dispose()

if __name__ == "__main__":
    asyncio.run(add_logo_columns())
