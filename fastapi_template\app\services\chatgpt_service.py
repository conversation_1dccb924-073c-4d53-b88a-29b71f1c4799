# app/services/chatgpt_service.py
"""
Service pour l'amélioration de texte avec ChatGPT
"""

import asyncio
import logging
from typing import Dict, Optional
from datetime import datetime
import openai
from openai import AsyncOpenAI

from app.core.config import settings
from app.models.document import DocumentType

logger = logging.getLogger(__name__)

class ChatGPTService:
    """Service pour l'amélioration de texte avec l'API OpenAI"""
    
    def __init__(self):
        # Charger la clé API depuis les variables d'environnement
        # Variable d'environnement requise: OPENAI_API_KEY
        self.api_key = settings.OPENAI_API_KEY
        self.model = getattr(settings, 'OPENAI_MODEL', 'gpt-4.1')
        self.max_retries = 3
        self.timeout = 30
        self.client = None

        # Initialiser le client seulement si la clé API est disponible
        if self.api_key and self.api_key != "your-openai-api-key-here":
            try:
                self.client = AsyncOpenAI(api_key=self.api_key)
                logger.info("Service ChatGPT initialisé avec succès")
            except Exception as e:
                logger.warning(f"Impossible d'initialiser le service ChatGPT: {e}")
                self.client = None
        else:
            logger.warning("Clé API OpenAI non configurée - Service ChatGPT désactivé")
            logger.info("Pour activer ChatGPT, définissez la variable d'environnement OPENAI_API_KEY")
        
        # Configuration des prompts par type de document
        self.prompts = {
            DocumentType.CCTP: {
                "Ameliore": """Tu es un expert en rédaction de Cahiers des Clauses Techniques Particulières (CCTP) pour le BTP.
Améliore le texte suivant en respectant ces critères :
- Précision technique et conformité aux normes
- Clarté des spécifications
- Respect du vocabulaire technique BTP
- Structure logique et cohérente

Texte à améliorer : {text}

Contexte supplémentaire : {context}

Réponds uniquement avec le texte amélioré, sans commentaires.""",

                "rephrase": """Tu es un expert en rédaction technique BTP. Reformule le texte suivant pour un CCTP en :
- Utilisant un vocabulaire technique précis
- Améliorant la clarté sans perdre le sens
- Respectant les standards de rédaction CCTP

Texte à reformuler : {text}

Réponds uniquement avec le texte reformulé.""",

                "expand": """Tu es un expert en CCTP. Développe le texte suivant en :
- Ajoutant des détails techniques pertinents
- Précisant les normes et références applicables
- Détaillant les exigences de mise en œuvre
- Conservant la structure existante

Texte à développer : {text}

Contexte : {context}

Réponds uniquement avec le texte développé.""",

                "simplify": """Tu es un expert en rédaction technique. Simplifie le texte suivant pour un CCTP en :
- Gardant l'essentiel technique
- Utilisant des phrases plus courtes
- Conservant la précision nécessaire
- Améliorant la lisibilité

Texte à simplifier : {text}

Réponds uniquement avec le texte simplifié."""
            },
            
            DocumentType.DPGF: {
                "enhance": """Tu es un expert en Décomposition du Prix Global et Forfaitaire (DPGF) pour le BTP.
Améliore le texte suivant en respectant ces critères :
- Précision des quantités et unités
- Clarté des postes de travaux
- Cohérence avec les pratiques DPGF
- Structure méthodique

Texte à améliorer : {text}

Contexte supplémentaire : {context}

Réponds uniquement avec le texte amélioré, sans commentaires.""",

                "rephrase": """Tu es un expert en métrés et DPGF. Reformule le texte suivant en :
- Utilisant la terminologie standard des métrés
- Précisant les unités de mesure
- Améliorant la structure des postes

Texte à reformuler : {text}

Réponds uniquement avec le texte reformulé.""",

                "expand": """Tu es un expert en DPGF. Développe le texte suivant en :
- Détaillant les sous-postes si nécessaire
- Précisant les modes de calcul
- Ajoutant les références aux prix unitaires
- Explicitant les conditions d'exécution

Texte à développer : {text}

Contexte : {context}

Réponds uniquement avec le texte développé.""",

                "simplify": """Tu es un expert en métrés. Simplifie le texte suivant pour un DPGF en :
- Gardant l'essentiel des quantités
- Utilisant des formulations claires
- Conservant la précision des mesures
- Améliorant la lisibilité

Texte à simplifier : {text}

Réponds uniquement avec le texte simplifié."""
            }
        }

    def is_configured(self) -> bool:
        """Vérifie si le service ChatGPT est correctement configuré"""
        return self.client is not None and self.api_key is not None

    def get_configuration_status(self) -> Dict[str, any]:
        """Retourne le statut de configuration du service"""
        return {
            "configured": self.is_configured(),
            "api_key_present": bool(self.api_key and self.api_key != "your-openai-api-key-here"),
            "model": self.model,
            "client_initialized": self.client is not None
        }

    async def enhance_text(
        self, 
        text: str, 
        prompt_type: str, 
        document_type: DocumentType,
        context: Optional[str] = None
    ) -> Dict[str, any]:
        """
        Améliore un texte avec ChatGPT
        
        Args:
            text: Texte à améliorer
            prompt_type: Type de prompt (enhance, rephrase, expand, simplify)
            document_type: Type de document (CCTP, DPGF)
            context: Contexte supplémentaire optionnel
            
        Returns:
            Dict contenant le texte amélioré et les métadonnées
        """
        start_time = datetime.now()

        try:
            # Vérifier que le service est disponible
            if not self.client:
                raise ValueError("Service ChatGPT non disponible - Clé API non configurée")

            # Validation des paramètres
            if not text.strip():
                raise ValueError("Le texte ne peut pas être vide")
            
            if document_type not in self.prompts:
                raise ValueError(f"Type de document non supporté: {document_type}")
            
            if prompt_type not in self.prompts[document_type]:
                raise ValueError(f"Type de prompt non supporté: {prompt_type}")
            
            # Construire le prompt
            prompt_template = self.prompts[document_type][prompt_type]
            prompt = prompt_template.format(
                text=text,
                context=context or "Aucun contexte spécifique"
            )
            
            # Appel à l'API OpenAI avec retry
            enhanced_text = await self._call_openai_with_retry(prompt)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"Texte amélioré avec succès - Type: {prompt_type}, Document: {document_type}, Temps: {processing_time:.2f}s")
            
            return {
                "original_text": text,
                "enhanced_text": enhanced_text,
                "prompt_type": prompt_type,
                "document_type": document_type,
                "processing_time": processing_time,
                "model_used": self.model,
                "success": True
            }
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Erreur lors de l'amélioration du texte: {str(e)}")
            
            return {
                "original_text": text,
                "enhanced_text": text,  # Retourner le texte original en cas d'erreur
                "prompt_type": prompt_type,
                "document_type": document_type,
                "processing_time": processing_time,
                "model_used": self.model,
                "success": False,
                "error": str(e)
            }

    async def _call_openai_with_retry(self, prompt: str) -> str:
        """
        Appelle l'API OpenAI avec mécanisme de retry
        """
        last_exception = None
        
        for attempt in range(self.max_retries):
            try:
                response = await asyncio.wait_for(
                    self.client.chat.completions.create(
                        model=self.model,
                        messages=[
                            {
                                "role": "system",
                                "content": "Tu es un expert en rédaction technique pour le secteur du BTP. Tu réponds toujours en français et de manière précise."
                            },
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        max_tokens=2000,
                        temperature=0.3,  # Peu de créativité pour la précision technique
                        top_p=0.9
                    ),
                    timeout=self.timeout
                )
                
                return response.choices[0].message.content.strip()
                
            except asyncio.TimeoutError as e:
                last_exception = e
                logger.warning(f"Timeout lors de l'appel OpenAI (tentative {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # Backoff exponentiel
                    
            except openai.RateLimitError as e:
                last_exception = e
                logger.warning(f"Rate limit atteint (tentative {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(5 * (attempt + 1))  # Attente plus longue pour rate limit
                    
            except openai.APIError as e:
                last_exception = e
                logger.error(f"Erreur API OpenAI (tentative {attempt + 1}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
                    
            except Exception as e:
                last_exception = e
                logger.error(f"Erreur inattendue (tentative {attempt + 1}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1)
        
        # Si tous les essais ont échoué
        raise last_exception or Exception("Échec de tous les appels à l'API OpenAI")

    def get_available_prompt_types(self, document_type: DocumentType) -> list:
        """Retourne la liste des types de prompts disponibles pour un type de document"""
        return list(self.prompts.get(document_type, {}).keys())

    def get_supported_document_types(self) -> list:
        """Retourne la liste des types de documents supportés"""
        return list(self.prompts.keys())

    def is_available(self) -> bool:
        """Vérifie si le service ChatGPT est disponible"""
        return self.client is not None

# Instance globale du service
chatgpt_service = ChatGPTService()
