# app/api/api_v1/endpoints/documents.py
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
import os
import uuid
from datetime import datetime

from app.api import deps
from app.models.user import User
from app.models.document import Document, DocumentVersion, DocumentFolder
from app.schemas.document import Document as DocumentSchema, DocumentCreate, DocumentUpdate, DocumentVersion as DocumentVersionSchema, DocumentFolder as DocumentFolderSchema
from app.core.config import settings

router = APIRouter()

@router.get("/", response_model=List[DocumentSchema])
def read_documents(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    company_id: int = None,
    category: str = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve documents.
    """
    if company_id:
        deps.get_company_access(company_id, current_user, db)
        query = db.query(Document).filter(Document.company_id == company_id)
    else:
        from app.models.workspace import UserCompany
        user_companies = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).all()
        company_ids = [uc.company_id for uc in user_companies]
        query = db.query(Document).filter(Document.company_id.in_(company_ids))
    
    if category:
        query = query.filter(Document.category == category)
    
    documents = query.offset(skip).limit(limit).all()
    return documents

@router.post("/upload", response_model=DocumentSchema)
async def upload_document(
    *,
    db: Session = Depends(deps.get_db),
    company_id: int,
    category: str = None,
    description: str = None,
    file: UploadFile = File(...),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Upload new document.
    """
    deps.get_company_access(company_id, current_user, db)
    
    # Validate file size
    if file.size > settings.MAX_FILE_SIZE:
        raise HTTPException(status_code=400, detail="File too large")
    
    # Create upload directory if it doesn't exist
    upload_dir = os.path.join(settings.UPLOAD_FOLDER, str(company_id))
    os.makedirs(upload_dir, exist_ok=True)
    
    # Generate unique filename
    file_extension = os.path.splitext(file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(upload_dir, unique_filename)
    
    # Save file
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # Create document record
    document = Document(
        company_id=company_id,
        name=file.filename,
        original_name=file.filename,
        file_path=file_path,
        file_size=len(content),
        mime_type=file.content_type,
        category=category,
        description=description,
        uploaded_by=current_user.id
    )
    db.add(document)
    db.commit()
    db.refresh(document)
    
    return document

@router.get("/{id}", response_model=DocumentSchema)
def read_document(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get document by ID.
    """
    document = db.query(Document).filter(Document.id == id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    deps.get_company_access(document.company_id, current_user, db)
    return document

@router.put("/{id}", response_model=DocumentSchema)
def update_document(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    document_in: DocumentUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a document.
    """
    document = db.query(Document).filter(Document.id == id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    deps.get_company_access(document.company_id, current_user, db)
    
    for field, value in document_in.dict(exclude_unset=True).items():
        setattr(document, field, value)
    
    db.add(document)
    db.commit()
    db.refresh(document)
    return document

@router.delete("/{id}")
def delete_document(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a document.
    """
    document = db.query(Document).filter(Document.id == id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    deps.get_company_access(document.company_id, current_user, db)
    
    # Delete file from disk
    if os.path.exists(document.file_path):
        os.remove(document.file_path)
    
    # Mark as inactive instead of deleting
    document.is_active = False
    db.add(document)
    db.commit()
    
    return {"message": "Document deleted successfully"}

@router.post("/{id}/versions", response_model=DocumentVersionSchema)
async def create_document_version(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    version_number: str,
    changes_description: str = None,
    file: UploadFile = File(...),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new version of document.
    """
    document = db.query(Document).filter(Document.id == id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    deps.get_company_access(document.company_id, current_user, db)
    
    # Validate file size
    if file.size > settings.MAX_FILE_SIZE:
        raise HTTPException(status_code=400, detail="File too large")
    
    # Create upload directory if it doesn't exist
    upload_dir = os.path.join(settings.UPLOAD_FOLDER, str(document.company_id), "versions")
    os.makedirs(upload_dir, exist_ok=True)
    
    # Generate unique filename
    file_extension = os.path.splitext(file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(upload_dir, unique_filename)
    
    # Save file
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # Create version record
    version = DocumentVersion(
        document_id=id,
        version_number=version_number,
        file_path=file_path,
        file_size=len(content),
        uploaded_by=current_user.id,
        changes_description=changes_description
    )
    db.add(version)
    db.commit()
    db.refresh(version)
    
    return version

@router.get("/{id}/versions", response_model=List[DocumentVersionSchema])
def read_document_versions(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get document versions.
    """
    document = db.query(Document).filter(Document.id == id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    deps.get_company_access(document.company_id, current_user, db)
    
    versions = db.query(DocumentVersion).filter(DocumentVersion.document_id == id).all()
    return versions

@router.get("/folders/", response_model=List[DocumentFolderSchema])
def read_document_folders(
    db: Session = Depends(deps.get_db),
    company_id: int = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve document folders.
    """
    if company_id:
        deps.get_company_access(company_id, current_user, db)
        # Get documents for the company and their folders
        documents = db.query(Document).filter(Document.company_id == company_id).all()
        document_ids = [doc.id for doc in documents]
        folders = db.query(DocumentFolder).filter(DocumentFolder.document_id.in_(document_ids)).all()
    else:
        from app.models.workspace import UserCompany
        user_companies = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).all()
        company_ids = [uc.company_id for uc in user_companies]
        documents = db.query(Document).filter(Document.company_id.in_(company_ids)).all()
        document_ids = [doc.id for doc in documents]
        folders = db.query(DocumentFolder).filter(DocumentFolder.document_id.in_(document_ids)).all()
    
    return folders

@router.post("/folders/", response_model=DocumentFolderSchema)
def create_document_folder(
    *,
    db: Session = Depends(deps.get_db),
    folder_in: DocumentFolderSchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create document folder.
    """
    document = db.query(Document).filter(Document.id == folder_in.document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    deps.get_company_access(document.company_id, current_user, db)
    
    folder = DocumentFolder(**folder_in.dict(exclude={'id'}))
    db.add(folder)
    db.commit()
    db.refresh(folder)
    return folder