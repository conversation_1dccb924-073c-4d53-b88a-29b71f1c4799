# app/api/api_v1/endpoints/entreprises_tiers.py
"""
API endpoints pour les entreprises tierces (carnet d'adresses)
"""

from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
import os
import uuid
from pathlib import Path

from app.api import deps
from app.models.user import User
from app.models.workspace import UserWorkspace, Workspace
from app.models.entreprise_tiers import EntrepriseTiers
from app.schemas.entreprise_tiers import (
    EntrepriseTiersCreate,
    EntrepriseTiersUpdate,
    EntrepriseTiersResponse,
    EntrepriseTiersList,
    EntrepriseTiersStats
)
from app.core.jwt_auth import require_auth

router = APIRouter()


@router.get("/", response_model=List[EntrepriseTiersResponse])
async def get_entreprises_tiers(
    db: AsyncSession = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="Nombre d'éléments à ignorer"),
    limit: int = Query(100, ge=1, le=1000, description="Nombre maximum d'éléments à retourner"),
    search: Optional[str] = Query(None, description="Recherche dans nom, email, SIRET, ville"),
    is_active: Optional[bool] = Query(None, description="Filtrer par statut actif/inactif"),
    activite: Optional[str] = Query(None, description="Filtrer par activité"),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Récupérer la liste des entreprises tierces avec pagination et filtres"""
    try:
        print(f"🔍 GET /entreprises-tiers - User: {current_user}")

        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur via UserWorkspace
        user_company_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_company_result.scalar_one_or_none()
        if not user_workspace:
            raise HTTPException(status_code=403, detail="Utilisateur non associé à un espace de travail")

        # Construire la requête de base
        query = select(EntrepriseTiers).where(
            and_(
                EntrepriseTiers.workspace_id == user_workspace.workspace_id,
                EntrepriseTiers.is_active == True if is_active is None else EntrepriseTiers.is_active == is_active
            )
        )

        # Ajouter les filtres
        if search:
            search_filter = or_(
                EntrepriseTiers.nom_entreprise.ilike(f"%{search}%"),
                EntrepriseTiers.email.ilike(f"%{search}%"),
                EntrepriseTiers.siret.ilike(f"%{search}%"),
                EntrepriseTiers.ville.ilike(f"%{search}%")
            )
            query = query.where(search_filter)

        if activite:
            query = query.where(EntrepriseTiers.activite.ilike(f"%{activite}%"))

        # Ajouter pagination
        query = query.offset(skip).limit(limit)

        # Exécuter la requête
        result = await db.execute(query)
        entreprises = result.scalars().all()

        print(f"✅ Found {len(entreprises)} entreprises tierces")
        return entreprises

    except Exception as e:
        print(f"❌ Error in get_entreprises_tiers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des entreprises tierces: {str(e)}")


@router.get("/{entreprise_id}", response_model=EntrepriseTiersResponse)
async def get_entreprise_tiers(
    entreprise_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Récupérer une entreprise tierce par son ID"""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur via UserWorkspace
        user_company_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_company_result.scalar_one_or_none()
        if not user_workspace:
            raise HTTPException(status_code=403, detail="Utilisateur non associé à un espace de travail")

        # Récupérer l'entreprise tierce
        result = await db.execute(
            select(EntrepriseTiers).where(
                and_(
                    EntrepriseTiers.id == entreprise_id,
                    EntrepriseTiers.workspace_id == user_workspace.workspace_id,
                    EntrepriseTiers.is_active == True
                )
            )
        )
        entreprise = result.scalar_one_or_none()

        if not entreprise:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entreprise tierce non trouvée"
            )

        return entreprise

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error in get_entreprise_tiers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération de l'entreprise tierce: {str(e)}")


@router.post("/", response_model=EntrepriseTiersResponse, status_code=status.HTTP_201_CREATED)
async def create_entreprise_tiers(
    *,
    db: AsyncSession = Depends(deps.get_db),
    entreprise_in: EntrepriseTiersCreate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Créer une nouvelle entreprise tierce"""
    try:
        print(f"🔍 POST /entreprises-tiers - Data received: {entreprise_in.dict()}")

        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur via UserWorkspace
        user_company_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_company_result.scalar_one_or_none()
        if not user_workspace:
            raise HTTPException(status_code=403, detail="Utilisateur non associé à un espace de travail")

        # Vérifier l'unicité du SIRET si fourni
        if entreprise_in.siret:
            existing_result = await db.execute(
                select(EntrepriseTiers).where(
                    and_(
                        EntrepriseTiers.siret == entreprise_in.siret,
                        EntrepriseTiers.workspace_id == user_workspace.workspace_id,
                        EntrepriseTiers.is_active == True
                    )
                )
            )
            existing = existing_result.scalar_one_or_none()
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Une entreprise avec le SIRET {entreprise_in.siret} existe déjà"
                )

        # Créer l'entreprise tierce
        entreprise_data = entreprise_in.dict(exclude_unset=True)
        entreprise_data["workspace_id"] = user_workspace.workspace_id
        entreprise_data["created_by"] = user_id

        db_entreprise = EntrepriseTiers(**entreprise_data)
        db.add(db_entreprise)
        await db.commit()
        await db.refresh(db_entreprise)

        print(f"✅ Created entreprise tierce: {db_entreprise.nom_entreprise}")
        return db_entreprise

    except HTTPException:
        raise
    except ValueError as e:
        print(f"❌ Validation error in create_entreprise_tiers: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"❌ Error in create_entreprise_tiers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la création de l'entreprise tierce: {str(e)}")


@router.put("/{entreprise_id}", response_model=EntrepriseTiersResponse)
async def update_entreprise_tiers(
    *,
    entreprise_id: int,
    db: AsyncSession = Depends(deps.get_db),
    entreprise_in: EntrepriseTiersUpdate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Mettre à jour une entreprise tierce"""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur via UserWorkspace
        user_company_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_company_result.scalar_one_or_none()
        if not user_workspace:
            raise HTTPException(status_code=403, detail="Utilisateur non associé à un espace de travail")

        # Récupérer l'entreprise tierce existante
        result = await db.execute(
            select(EntrepriseTiers).where(
                and_(
                    EntrepriseTiers.id == entreprise_id,
                    EntrepriseTiers.workspace_id == user_workspace.workspace_id,
                    EntrepriseTiers.is_active == True
                )
            )
        )
        db_entreprise = result.scalar_one_or_none()

        if not db_entreprise:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entreprise tierce non trouvée"
            )

        # Vérifier l'unicité du SIRET si modifié
        if entreprise_in.siret and entreprise_in.siret != db_entreprise.siret:
            existing_result = await db.execute(
                select(EntrepriseTiers).where(
                    and_(
                        EntrepriseTiers.siret == entreprise_in.siret,
                        EntrepriseTiers.workspace_id == user_workspace.workspace_id,
                        EntrepriseTiers.id != entreprise_id,
                        EntrepriseTiers.is_active == True
                    )
                )
            )
            existing = existing_result.scalar_one_or_none()
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Une entreprise avec le SIRET {entreprise_in.siret} existe déjà"
                )

        # Mettre à jour les champs modifiés
        update_data = entreprise_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_entreprise, field, value)

        await db.commit()
        await db.refresh(db_entreprise)

        print(f"✅ Updated entreprise tierce: {db_entreprise.nom_entreprise}")
        return db_entreprise

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error in update_entreprise_tiers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la mise à jour de l'entreprise tierce: {str(e)}")


@router.delete("/{entreprise_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_entreprise_tiers(
    *,
    entreprise_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
):
    """Supprimer une entreprise tierce (soft delete)"""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur via UserWorkspace
        user_company_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_company_result.scalar_one_or_none()
        if not user_workspace:
            raise HTTPException(status_code=403, detail="Utilisateur non associé à un espace de travail")

        # Récupérer l'entreprise tierce existante
        result = await db.execute(
            select(EntrepriseTiers).where(
                and_(
                    EntrepriseTiers.id == entreprise_id,
                    EntrepriseTiers.workspace_id == user_workspace.workspace_id,
                    EntrepriseTiers.is_active == True
                )
            )
        )
        db_entreprise = result.scalar_one_or_none()

        if not db_entreprise:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entreprise tierce non trouvée"
            )

        # Soft delete
        db_entreprise.is_active = False
        await db.commit()

        print(f"✅ Deleted entreprise tierce: {db_entreprise.nom_entreprise}")

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error in delete_entreprise_tiers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la suppression de l'entreprise tierce: {str(e)}")


@router.post("/{entreprise_id}/upload-logo", response_model=EntrepriseTiersResponse)
async def upload_logo(
    *,
    entreprise_id: int,
    file: UploadFile = File(...),
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Upload du logo pour une entreprise tierce"""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Vérifier le type de fichier
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"]
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"Type de fichier non autorisé. Types acceptés: {', '.join(allowed_types)}"
            )

        # Vérifier la taille du fichier (max 5MB)
        max_size = 5 * 1024 * 1024  # 5MB
        file_content = await file.read()
        if len(file_content) > max_size:
            raise HTTPException(status_code=400, detail="Le fichier est trop volumineux (max 5MB)")

        # Récupérer l'espace de travail de l'utilisateur
        user_company_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_company_result.scalar_one_or_none()
        if not user_workspace:
            raise HTTPException(status_code=403, detail="Utilisateur non associé à un espace de travail")

        # Récupérer l'entreprise tierce
        result = await db.execute(
            select(EntrepriseTiers).where(
                and_(
                    EntrepriseTiers.id == entreprise_id,
                    EntrepriseTiers.workspace_id == user_workspace.workspace_id,
                    EntrepriseTiers.is_active == True
                )
            )
        )
        entreprise = result.scalar_one_or_none()
        if not entreprise:
            raise HTTPException(status_code=404, detail="Entreprise tierce non trouvée")

        # Créer le répertoire de stockage s'il n'existe pas
        upload_dir = Path("uploads/logos/entreprises")
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Générer un nom de fichier unique
        file_extension = Path(file.filename).suffix.lower()
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = upload_dir / unique_filename

        # Sauvegarder le fichier
        with open(file_path, "wb") as buffer:
            buffer.write(file_content)

        # Mettre à jour l'entreprise avec les informations du logo
        entreprise.logo_url = f"/uploads/logos/entreprises/{unique_filename}"
        entreprise.logo_filename = file.filename

        await db.commit()
        await db.refresh(entreprise)

        print(f"✅ Logo uploaded for entreprise: {entreprise.nom_entreprise}")
        return entreprise

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error in upload_logo: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de l'upload du logo: {str(e)}")


@router.delete("/{entreprise_id}/logo", response_model=EntrepriseTiersResponse)
async def delete_logo(
    *,
    entreprise_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Supprimer le logo d'une entreprise tierce"""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        user_company_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_company_result.scalar_one_or_none()
        if not user_workspace:
            raise HTTPException(status_code=403, detail="Utilisateur non associé à un espace de travail")

        # Récupérer l'entreprise tierce
        result = await db.execute(
            select(EntrepriseTiers).where(
                and_(
                    EntrepriseTiers.id == entreprise_id,
                    EntrepriseTiers.workspace_id == user_workspace.workspace_id,
                    EntrepriseTiers.is_active == True
                )
            )
        )
        entreprise = result.scalar_one_or_none()
        if not entreprise:
            raise HTTPException(status_code=404, detail="Entreprise tierce non trouvée")

        # Supprimer le fichier physique si il existe
        if entreprise.logo_url:
            file_path = Path(f"uploads/logos/entreprises/{Path(entreprise.logo_url).name}")
            if file_path.exists():
                file_path.unlink()

        # Supprimer les références du logo
        entreprise.logo_url = None
        entreprise.logo_filename = None

        await db.commit()
        await db.refresh(entreprise)

        print(f"✅ Logo deleted for entreprise: {entreprise.nom_entreprise}")
        return entreprise

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error in delete_logo: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la suppression du logo: {str(e)}")
