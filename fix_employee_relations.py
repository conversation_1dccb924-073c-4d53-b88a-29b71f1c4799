#!/usr/bin/env python3
"""
Script pour corriger les relations Employee
Les employés doivent être associés aux entreprises_tiers, pas aux workspaces
"""

import asyncio
import asyncpg
from datetime import datetime

DATABASE_URL = "**************************************************************************************************/postgres"

async def fix_employee_relations():
    """Corriger les relations Employee"""
    print("🔧 Correction des relations Employee...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # 1. Vérifier si la colonne workspace_id existe encore
        workspace_id_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'employees' AND column_name = 'workspace_id'
            )
        """)
        
        if not workspace_id_exists:
            print("   ✅ La colonne workspace_id n'existe plus, rien à faire")
            return
        
        print(f"   🔍 Colonne workspace_id trouvée, correction nécessaire")
        
        # 2. Vérifier si la colonne entreprise_tiers_id existe déjà
        entreprise_tiers_id_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'employees' AND column_name = 'entreprise_tiers_id'
            )
        """)
        
        if not entreprise_tiers_id_exists:
            print("   🔧 Ajout de la colonne entreprise_tiers_id...")
            await conn.execute("ALTER TABLE employees ADD COLUMN entreprise_tiers_id INTEGER")
        
        # 3. Créer une entreprise_tiers par défaut si elle n'existe pas
        default_entreprise = await conn.fetchval("""
            SELECT id FROM entreprises_tiers 
            WHERE nom_entreprise = 'Entreprise par défaut'
        """)
        
        if not default_entreprise:
            print("   🏢 Création d'une entreprise_tiers par défaut...")
            
            # Récupérer le premier workspace
            first_workspace = await conn.fetchval("SELECT id FROM workspaces ORDER BY id LIMIT 1")
            if not first_workspace:
                print("   ❌ Aucun workspace trouvé, impossible de créer l'entreprise par défaut")
                return
            
            default_entreprise = await conn.fetchval("""
                INSERT INTO entreprises_tiers (
                    nom_entreprise, 
                    activite, 
                    adresse, 
                    code_postal, 
                    ville, 
                    telephone, 
                    email, 
                    siret,
                    workspace_id,
                    created_at,
                    updated_at
                ) VALUES (
                    'Entreprise par défaut',
                    'Non spécifiée',
                    'Adresse non spécifiée',
                    '00000',
                    'Ville non spécifiée',
                    'Non spécifié',
                    '<EMAIL>',
                    'SIRET_DEFAULT',
                    $1,
                    $2,
                    $2
                ) RETURNING id
            """, first_workspace, datetime.utcnow())
            
            print(f"   ✅ Entreprise par défaut créée avec l'ID {default_entreprise}")
        else:
            print(f"   ✅ Entreprise par défaut trouvée avec l'ID {default_entreprise}")
        
        # 4. Compter les employés à migrer
        employee_count = await conn.fetchval("""
            SELECT COUNT(*) FROM employees 
            WHERE entreprise_tiers_id IS NULL
        """)
        
        if employee_count > 0:
            print(f"   🔄 Migration de {employee_count} employé(s)...")
            
            # 5. Mettre à jour tous les employés pour qu'ils pointent vers cette entreprise_tiers
            updated_count = await conn.execute("""
                UPDATE employees 
                SET entreprise_tiers_id = $1
                WHERE entreprise_tiers_id IS NULL
            """, default_entreprise)
            
            print(f"   ✅ {employee_count} employé(s) migré(s)")
        else:
            print("   ✅ Tous les employés sont déjà associés à une entreprise_tiers")
        
        # 6. Ajouter la contrainte NOT NULL si nécessaire
        constraint_exists = await conn.fetchval("""
            SELECT is_nullable FROM information_schema.columns 
            WHERE table_name = 'employees' AND column_name = 'entreprise_tiers_id'
        """)
        
        if constraint_exists == 'YES':
            print("   🔧 Ajout de la contrainte NOT NULL...")
            await conn.execute("ALTER TABLE employees ALTER COLUMN entreprise_tiers_id SET NOT NULL")
        
        # 7. Vérifier si la contrainte FK existe déjà
        fk_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.table_constraints 
                WHERE table_name = 'employees' 
                AND constraint_name = 'fk_employees_entreprise_tiers'
            )
        """)
        
        if not fk_exists:
            print("   🔧 Ajout de la contrainte de clé étrangère...")
            await conn.execute("""
                ALTER TABLE employees 
                ADD CONSTRAINT fk_employees_entreprise_tiers 
                FOREIGN KEY (entreprise_tiers_id) REFERENCES entreprises_tiers(id)
            """)
        
        # 8. Supprimer l'ancienne colonne workspace_id
        print("   🗑️ Suppression de l'ancienne colonne workspace_id...")
        await conn.execute("ALTER TABLE employees DROP COLUMN workspace_id")
        
        # 9. Créer un index pour les performances si nécessaire
        index_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM pg_indexes 
                WHERE tablename = 'employees' 
                AND indexname = 'idx_employees_entreprise_tiers_id'
            )
        """)
        
        if not index_exists:
            print("   📊 Création de l'index pour les performances...")
            await conn.execute("CREATE INDEX idx_employees_entreprise_tiers_id ON employees(entreprise_tiers_id)")
        
        print("   ✅ Relations Employee corrigées avec succès!")
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        raise
    finally:
        await conn.close()

async def verify_relations():
    """Vérifier que les relations sont correctes"""
    print("\n🔍 Vérification des relations...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier la structure de la table employees
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'employees'
            ORDER BY ordinal_position
        """)
        
        print("   📋 Structure de la table employees:")
        for col in columns:
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            print(f"     • {col['column_name']}: {col['data_type']} {nullable}")
        
        # Vérifier les contraintes FK
        fk_constraints = await conn.fetch("""
            SELECT constraint_name, column_name, foreign_table_name, foreign_column_name
            FROM information_schema.key_column_usage kcu
            JOIN information_schema.referential_constraints rc 
                ON kcu.constraint_name = rc.constraint_name
            JOIN information_schema.key_column_usage fkcu 
                ON rc.unique_constraint_name = fkcu.constraint_name
            WHERE kcu.table_name = 'employees'
        """)
        
        print("   🔗 Contraintes de clé étrangère:")
        for fk in fk_constraints:
            print(f"     • {fk['column_name']} → {fk['foreign_table_name']}.{fk['foreign_column_name']}")
        
        # Compter les employés par entreprise_tiers
        employee_stats = await conn.fetch("""
            SELECT et.nom_entreprise, COUNT(e.id) as employee_count
            FROM entreprises_tiers et
            LEFT JOIN employees e ON et.id = e.entreprise_tiers_id
            GROUP BY et.id, et.nom_entreprise
            ORDER BY employee_count DESC
        """)
        
        print("   📊 Répartition des employés par entreprise_tiers:")
        for stat in employee_stats:
            print(f"     • {stat['nom_entreprise']}: {stat['employee_count']} employé(s)")
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    finally:
        await conn.close()

async def main():
    """Fonction principale"""
    print("🔧 CORRECTION DES RELATIONS EMPLOYEE")
    print("="*50)
    
    try:
        await fix_employee_relations()
        await verify_relations()
        
        print("\n🎉 Correction terminée avec succès!")
        print("✅ Les employés sont maintenant associés aux entreprises_tiers")
        print("✅ L'ancienne relation avec workspaces a été supprimée")
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la correction: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
