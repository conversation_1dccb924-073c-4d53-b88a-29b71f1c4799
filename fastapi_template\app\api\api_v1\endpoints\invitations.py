# app/api/v1/invitations.py
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from datetime import datetime, timedelta
import secrets
import uuid

from app.core.database import get_db
from app.api.deps import get_current_active_user, require_admin_role
from app.models.user import User
from app.models.workspace import Workspace, UserWorkspace, WorkspaceRole
from app.models.audit import WorkspaceInvitation, AuditAction
# Alias de compatibilité
from app.models.workspace import Company, UserCompany, CompanyRole
from app.models.audit import WorkspaceInvitation as CompanyInvitation
from app.services.audit_service import AuditService
from app.middleware.tenant_middleware import get_current_company_id
from app.services.supabase_service import SupabaseService

router = APIRouter()


class InvitationCreate:
    def __init__(self, email: str, role: CompanyRole = CompanyRole.USER):
        self.email = email
        self.role = role


class InvitationResponse:
    def __init__(self, id: int, email: str, role: str, expires_at: datetime, 
                 is_pending: bool, invited_by_name: str, created_at: datetime):
        self.id = id
        self.email = email
        self.role = role
        self.expires_at = expires_at
        self.is_pending = is_pending
        self.invited_by_name = invited_by_name
        self.created_at = created_at


@router.post("/", response_model=dict)
async def create_invitation(
    request: Request,
    invitation_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
    _: str = Depends(require_admin_role)
):
    """
    Créer une invitation pour un nouvel utilisateur
    """
    company_id = get_current_company_id(request)
    if not company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="No company context available"
        )
    
    email = invitation_data.get("email")
    role = invitation_data.get("role", "user")
    
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email is required"
        )
    
    # Vérifier si l'utilisateur existe déjà dans cette entreprise
    existing_user_company = await db.execute(
        select(UserCompany)
        .join(User, UserCompany.user_id == User.id)
        .where(
            and_(
                User.email == email,
                UserCompany.company_id == company_id
            )
        )
    )
    
    if existing_user_company.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already exists in this company"
        )
    
    # Vérifier si une invitation active existe déjà
    existing_invitation = await db.execute(
        select(CompanyInvitation)
        .where(
            and_(
                CompanyInvitation.email == email,
                CompanyInvitation.company_id == company_id,
                CompanyInvitation.is_active == True,
                CompanyInvitation.accepted_at.is_(None),
                CompanyInvitation.rejected_at.is_(None),
                CompanyInvitation.expires_at > datetime.utcnow()
            )
        )
    )
    
    if existing_invitation.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Active invitation already exists for this email"
        )
    
    # Créer l'invitation
    invitation_token = secrets.token_urlsafe(32)
    expires_at = datetime.utcnow() + timedelta(days=7)  # Expire dans 7 jours
    
    invitation = CompanyInvitation(
        company_id=company_id,
        email=email,
        role=role,
        invited_by=current_user.id,
        invitation_token=invitation_token,
        expires_at=expires_at
    )
    
    db.add(invitation)
    await db.commit()
    await db.refresh(invitation)
    
    # Log de l'audit
    await AuditService.log_invitation(
        db=db,
        inviter=current_user,
        company_id=company_id,
        invited_email=email,
        role=role
    )
    
    # TODO: Envoyer l'email d'invitation
    # await send_invitation_email(email, invitation_token, company_name)
    
    return {
        "message": "Invitation created successfully",
        "invitation_id": invitation.id,
        "expires_at": invitation.expires_at,
        "invitation_link": f"/accept-invitation?token={invitation_token}"
    }


@router.get("/", response_model=List[dict])
async def list_invitations(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
    _: str = Depends(require_admin_role)
):
    """
    Lister toutes les invitations pour l'entreprise courante
    """
    company_id = get_current_company_id(request)
    if not company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="No company context available"
        )
    
    result = await db.execute(
        select(CompanyInvitation, User)
        .join(User, CompanyInvitation.invited_by == User.id)
        .where(CompanyInvitation.company_id == company_id)
        .order_by(CompanyInvitation.created_at.desc())
    )
    
    invitations = []
    for invitation, inviter in result.all():
        invitations.append({
            "id": invitation.id,
            "email": invitation.email,
            "role": invitation.role,
            "expires_at": invitation.expires_at,
            "is_pending": invitation.is_pending,
            "is_expired": invitation.is_expired,
            "accepted_at": invitation.accepted_at,
            "rejected_at": invitation.rejected_at,
            "invited_by_name": f"{inviter.first_name} {inviter.last_name}",
            "created_at": invitation.created_at
        })
    
    return invitations


@router.post("/{invitation_id}/resend")
async def resend_invitation(
    invitation_id: int,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
    _: str = Depends(require_admin_role)
):
    """
    Renvoyer une invitation
    """
    company_id = get_current_company_id(request)
    if not company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="No company context available"
        )
    
    # Récupérer l'invitation
    result = await db.execute(
        select(CompanyInvitation)
        .where(
            and_(
                CompanyInvitation.id == invitation_id,
                CompanyInvitation.company_id == company_id
            )
        )
    )
    
    invitation = result.scalar_one_or_none()
    if not invitation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invitation not found"
        )
    
    if not invitation.is_pending:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot resend non-pending invitation"
        )
    
    # Générer un nouveau token et prolonger l'expiration
    invitation.invitation_token = secrets.token_urlsafe(32)
    invitation.expires_at = datetime.utcnow() + timedelta(days=7)
    invitation.updated_at = datetime.utcnow()
    
    await db.commit()
    
    # TODO: Renvoyer l'email d'invitation
    # await send_invitation_email(invitation.email, invitation.invitation_token, company_name)
    
    return {
        "message": "Invitation resent successfully",
        "expires_at": invitation.expires_at
    }


@router.delete("/{invitation_id}")
async def cancel_invitation(
    invitation_id: int,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
    _: str = Depends(require_admin_role)
):
    """
    Annuler une invitation
    """
    company_id = get_current_company_id(request)
    if not company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="No company context available"
        )
    
    # Récupérer l'invitation
    result = await db.execute(
        select(CompanyInvitation)
        .where(
            and_(
                CompanyInvitation.id == invitation_id,
                CompanyInvitation.company_id == company_id
            )
        )
    )
    
    invitation = result.scalar_one_or_none()
    if not invitation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invitation not found"
        )
    
    # Marquer comme inactive
    invitation.is_active = False
    invitation.updated_at = datetime.utcnow()
    
    await db.commit()
    
    return {"message": "Invitation cancelled successfully"}


@router.post("/accept/{token}")
async def accept_invitation(
    token: str,
    db: AsyncSession = Depends(get_db)
):
    """
    Accepter une invitation (endpoint public)
    """
    # Récupérer l'invitation par token
    result = await db.execute(
        select(CompanyInvitation, Company)
        .join(Company, CompanyInvitation.company_id == Company.id)
        .where(CompanyInvitation.invitation_token == token)
    )

    invitation_data = result.first()
    if not invitation_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invalid invitation token"
        )

    invitation, company = invitation_data

    if not invitation.is_pending:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invitation is no longer valid"
        )

    if invitation.is_expired:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invitation has expired"
        )

    return {
        "invitation": {
            "id": invitation.id,
            "email": invitation.email,
            "role": invitation.role,
            "company_name": company.name,
            "expires_at": invitation.expires_at
        },
        "company": {
            "id": company.id,
            "name": company.name,
            "address": company.address
        }
    }


@router.post("/confirm/{token}")
async def confirm_invitation(
    token: str,
    user_data: dict,
    db: AsyncSession = Depends(get_db)
):
    """
    Confirmer l'acceptation d'une invitation et créer l'utilisateur
    """
    # Récupérer l'invitation
    result = await db.execute(
        select(CompanyInvitation)
        .where(CompanyInvitation.invitation_token == token)
    )

    invitation = result.scalar_one_or_none()
    if not invitation or not invitation.is_pending or invitation.is_expired:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired invitation"
        )

    # Vérifier si l'utilisateur existe déjà dans Supabase
    # TODO: Intégrer avec Supabase Auth pour créer/récupérer l'utilisateur

    # Pour l'instant, créer un utilisateur local
    supabase_user_id = user_data.get("supabase_user_id")
    if not supabase_user_id:
        # Générer un ID temporaire
        supabase_user_id = str(uuid.uuid4())

    # Vérifier si l'utilisateur existe déjà localement
    existing_user = await db.execute(
        select(User).where(User.email == invitation.email)
    )
    user = existing_user.scalar_one_or_none()

    if not user:
        # Créer l'utilisateur
        user = User(
            supabase_user_id=supabase_user_id,
            email=invitation.email,
            first_name=user_data.get("first_name", ""),
            last_name=user_data.get("last_name", ""),
            role="USER",
            is_active=True,
            is_verified=True
        )
        db.add(user)
        await db.flush()  # Pour obtenir l'ID

    # Créer la relation utilisateur-entreprise
    user_company = UserCompany(
        user_id=user.id,
        company_id=invitation.company_id,
        role=invitation.role,
        invited_by=invitation.invited_by,
        invited_at=invitation.created_at,
        joined_at=datetime.utcnow(),
        is_default=True  # Première entreprise par défaut
    )

    db.add(user_company)

    # Marquer l'invitation comme acceptée
    invitation.accepted_at = datetime.utcnow()
    invitation.is_active = False

    await db.commit()

    # Log de l'audit
    await AuditService.log_action(
        db=db,
        company_id=invitation.company_id,
        action=AuditAction.ACCEPT_INVITE,
        resource_type="user_invitation",
        user_id=user.id,
        resource_id=str(invitation.id),
        details={
            "email": invitation.email,
            "role": invitation.role
        }
    )

    return {
        "message": "Invitation accepted successfully",
        "user_id": user.id,
        "company_id": invitation.company_id
    }
